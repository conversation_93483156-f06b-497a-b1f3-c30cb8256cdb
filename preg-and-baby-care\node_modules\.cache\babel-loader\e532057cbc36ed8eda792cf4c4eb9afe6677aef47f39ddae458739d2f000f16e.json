{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\final\\\\Upd\\\\preg-and-baby-care\\\\src\\\\components\\\\layout\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { faBabyCarriage, faBars, faSignInAlt } from \"@fortawesome/free-solid-svg-icons\";\nimport \"../../assets/css/Navbar.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const location = useLocation();\n  useEffect(() => {\n    // Check if user is logged in\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      setIsLoggedIn(true);\n    } else {\n      setIsLoggedIn(false);\n    }\n  }, [location]);\n\n  // Add event listener for mobile menu\n  useEffect(() => {\n    const handleResize = () => {\n      if (window.innerWidth > 768 && mobileMenuOpen) {\n        setMobileMenuOpen(false);\n      }\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, [mobileMenuOpen]);\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    setIsLoggedIn(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: faBabyCarriage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), \"Preg and Baby Care\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `nav-links ${mobileMenuOpen ? \"active\" : \"\"}`,\n      id: \"navLinks\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: location.pathname === \"/\" ? \"active\" : \"\",\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/pregnancy-care\",\n        className: location.pathname === \"/pregnancy-care\" ? \"active\" : \"\",\n        children: \"Pregnancy Care\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/baby-care\",\n        className: location.pathname === \"/baby-care\" ? \"active\" : \"\",\n        children: \"Baby Care\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/consult-doctor\",\n        className: location.pathname === \"/consult-doctor\" ? \"active\" : \"\",\n        children: \"Consult Doctor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/appointments\",\n        className: location.pathname === \"/appointments\" ? \"active\" : \"\",\n        children: \"My Appointments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/schemes\",\n        className: location.pathname === \"/schemes\" ? \"active\" : \"\",\n        children: \"Schemes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/contact\",\n        className: location.pathname === \"/contact\" ? \"active\" : \"\",\n        children: \"Contact\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-options\",\n        children: isLoggedIn ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"login-btn\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignInAlt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), \" Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"login-btn\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faSignInAlt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), \" Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"signup-btn\",\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"book-now\",\n        children: \"Get Started\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"mobile-menu-btn\",\n      onClick: toggleMobileMenu,\n      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: faBars\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"S3kvhjRF0G+v8UHRkkmbCTHzuFM=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "FontAwesomeIcon", "faBabyCarriage", "faBars", "faSignInAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "mobileMenuOpen", "setMobileMenuOpen", "isLoggedIn", "setIsLoggedIn", "location", "token", "localStorage", "getItem", "handleResize", "window", "innerWidth", "addEventListener", "removeEventListener", "toggleMobileMenu", "handleLogout", "removeItem", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "to", "pathname", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/components/layout/Navbar.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport {\n  faBabyCarriage,\n  faBars,\n  faSignInAlt,\n} from \"@fortawesome/free-solid-svg-icons\";\nimport \"../../assets/css/Navbar.css\";\n\nconst Navbar = () => {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const location = useLocation();\n\n  useEffect(() => {\n    // Check if user is logged in\n    const token = localStorage.getItem(\"token\");\n\n    if (token) {\n      setIsLoggedIn(true);\n    } else {\n      setIsLoggedIn(false);\n    }\n  }, [location]);\n\n  // Add event listener for mobile menu\n  useEffect(() => {\n    const handleResize = () => {\n      if (window.innerWidth > 768 && mobileMenuOpen) {\n        setMobileMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, [mobileMenuOpen]);\n\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    setIsLoggedIn(false);\n  };\n\n  return (\n    <header className=\"navbar\">\n      <div className=\"logo\">\n        <FontAwesomeIcon icon={faBabyCarriage} />\n        Preg and Baby Care\n      </div>\n\n      <nav\n        className={`nav-links ${mobileMenuOpen ? \"active\" : \"\"}`}\n        id=\"navLinks\"\n      >\n        <Link to=\"/\" className={location.pathname === \"/\" ? \"active\" : \"\"}>\n          Home\n        </Link>\n        <Link\n          to=\"/pregnancy-care\"\n          className={location.pathname === \"/pregnancy-care\" ? \"active\" : \"\"}\n        >\n          Pregnancy Care\n        </Link>\n        <Link\n          to=\"/baby-care\"\n          className={location.pathname === \"/baby-care\" ? \"active\" : \"\"}\n        >\n          Baby Care\n        </Link>\n        <Link\n          to=\"/consult-doctor\"\n          className={location.pathname === \"/consult-doctor\" ? \"active\" : \"\"}\n        >\n          Consult Doctor\n        </Link>\n        <Link\n          to=\"/appointments\"\n          className={location.pathname === \"/appointments\" ? \"active\" : \"\"}\n        >\n          My Appointments\n        </Link>\n        <Link\n          to=\"/schemes\"\n          className={location.pathname === \"/schemes\" ? \"active\" : \"\"}\n        >\n          Schemes\n        </Link>\n        <Link\n          to=\"/contact\"\n          className={location.pathname === \"/contact\" ? \"active\" : \"\"}\n        >\n          Contact\n        </Link>\n      </nav>\n\n      <div className=\"header-actions\">\n        <div className=\"login-options\">\n          {isLoggedIn ? (\n            <button onClick={handleLogout} className=\"login-btn\">\n              <FontAwesomeIcon icon={faSignInAlt} /> Logout\n            </button>\n          ) : (\n            <>\n              <Link to=\"/login\" className=\"login-btn\">\n                <FontAwesomeIcon icon={faSignInAlt} /> Login\n              </Link>\n              <Link to=\"/signup\" className=\"signup-btn\">\n                Sign Up\n              </Link>\n            </>\n          )}\n        </div>\n        <button className=\"book-now\">Get Started</button>\n      </div>\n\n      <button className=\"mobile-menu-btn\" onClick={toggleMobileMenu}>\n        <FontAwesomeIcon icon={faBars} />\n      </button>\n    </header>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,cAAc,EACdC,MAAM,EACNC,WAAW,QACN,mCAAmC;AAC1C,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,KAAK,EAAE;MACTF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACLA,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,QAAQ,CAAC,CAAC;;EAEd;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMqB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIC,MAAM,CAACC,UAAU,GAAG,GAAG,IAAIV,cAAc,EAAE;QAC7CC,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDQ,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,CAACR,cAAc,CAAC,CAAC;EAEpB,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7BZ,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzBR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BZ,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACER,OAAA;IAAQqB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBtB,OAAA;MAAKqB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBtB,OAAA,CAACL,eAAe;QAAC4B,IAAI,EAAE3B;MAAe;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,sBAE3C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEN3B,OAAA;MACEqB,SAAS,EAAE,aAAahB,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;MACzDuB,EAAE,EAAC,UAAU;MAAAN,QAAA,gBAEbtB,OAAA,CAACP,IAAI;QAACoC,EAAE,EAAC,GAAG;QAACR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EAAC;MAEnE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,iBAAiB;QACpBR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,iBAAiB,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EACpE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,YAAY;QACfR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EAC/D;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,iBAAiB;QACpBR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,iBAAiB,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EACpE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,eAAe;QAClBR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EAClE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,UAAU;QACbR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EAC7D;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;QACHoC,EAAE,EAAC,UAAU;QACbR,SAAS,EAAEZ,QAAQ,CAACqB,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;QAAAR,QAAA,EAC7D;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN3B,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtB,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3Bf,UAAU,gBACTP,OAAA;UAAQ+B,OAAO,EAAEZ,YAAa;UAACE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAClDtB,OAAA,CAACL,eAAe;YAAC4B,IAAI,EAAEzB;UAAY;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WACxC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET3B,OAAA,CAAAE,SAAA;UAAAoB,QAAA,gBACEtB,OAAA,CAACP,IAAI;YAACoC,EAAE,EAAC,QAAQ;YAACR,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrCtB,OAAA,CAACL,eAAe;cAAC4B,IAAI,EAAEzB;YAAY;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UACxC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP3B,OAAA,CAACP,IAAI;YAACoC,EAAE,EAAC,SAAS;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE1C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN3B,OAAA;QAAQqB,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAEN3B,OAAA;MAAQqB,SAAS,EAAC,iBAAiB;MAACU,OAAO,EAAEb,gBAAiB;MAAAI,QAAA,eAC5DtB,OAAA,CAACL,eAAe;QAAC4B,IAAI,EAAE1B;MAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACvB,EAAA,CAnHID,MAAM;EAAA,QAGOT,WAAW;AAAA;AAAAsC,EAAA,GAHxB7B,MAAM;AAqHZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}