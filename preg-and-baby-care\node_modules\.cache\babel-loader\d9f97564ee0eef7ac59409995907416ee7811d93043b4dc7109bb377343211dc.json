{"ast": null, "code": "import { isLeapYear } from \"./isLeapYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInYear} function options.\n */\n\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a year\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport function getDaysInYear(date, options) {\n  const _date = toDate(date, options?.in);\n  if (Number.isNaN(+_date)) return NaN;\n  return isLeapYear(_date) ? 366 : 365;\n}\n\n// Fallback for modularized imports:\nexport default getDaysInYear;", "map": {"version": 3, "names": ["isLeapYear", "toDate", "getDaysInYear", "date", "options", "_date", "in", "Number", "isNaN", "NaN"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/date-fns/getDaysInYear.js"], "sourcesContent": ["import { isLeapYear } from \"./isLeapYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInYear} function options.\n */\n\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a year\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport function getDaysInYear(date, options) {\n  const _date = toDate(date, options?.in);\n  if (Number.isNaN(+_date)) return NaN;\n  return isLeapYear(_date) ? 366 : 365;\n}\n\n// Fallback for modularized imports:\nexport default getDaysInYear;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,IAAIC,MAAM,CAACC,KAAK,CAAC,CAACH,KAAK,CAAC,EAAE,OAAOI,GAAG;EACpC,OAAOT,UAAU,CAACK,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;AACtC;;AAEA;AACA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}