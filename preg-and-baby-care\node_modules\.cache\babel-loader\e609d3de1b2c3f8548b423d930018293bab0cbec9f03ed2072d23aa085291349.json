{"ast": null, "code": "import { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isSameISOWeek} function options.\n */\n\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week (and year)\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\nexport function isSameISOWeek(laterDate, earlierDate, options) {\n  return isSameWeek(laterDate, earlierDate, {\n    ...options,\n    weekStartsOn: 1\n  });\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeek;", "map": {"version": 3, "names": ["isSameWeek", "isSameISOWeek", "laterDate", "earlierDate", "options", "weekStartsOn"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/date-fns/isSameISOWeek.js"], "sourcesContent": ["import { isSameWeek } from \"./isSameWeek.js\";\n\n/**\n * The {@link isSameISOWeek} function options.\n */\n\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week (and year)\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\nexport function isSameISOWeek(laterDate, earlierDate, options) {\n  return isSameWeek(laterDate, earlierDate, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeek;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC7D,OAAOJ,UAAU,CAACE,SAAS,EAAEC,WAAW,EAAE;IAAE,GAAGC,OAAO;IAAEC,YAAY,EAAE;EAAE,CAAC,CAAC;AAC5E;;AAEA;AACA,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}