{"ast": null, "code": "'use strict';\n\n/** @type {import('./ref')} */\nmodule.exports = ReferenceError;", "map": {"version": 3, "names": ["module", "exports", "ReferenceError"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/es-errors/ref.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./ref')} */\nmodule.exports = ReferenceError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}