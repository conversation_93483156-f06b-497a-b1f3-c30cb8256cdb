{"ast": null, "code": "/**\n * Module dependencies.\n */\n\nvar sep = require('path').sep || '/';\n\n/**\n * Module exports.\n */\n\nmodule.exports = fileUriToPath;\n\n/**\n * File URI to Path function.\n *\n * @param {String} uri\n * @return {String} path\n * @api public\n */\n\nfunction fileUriToPath(uri) {\n  if ('string' != typeof uri || uri.length <= 7 || 'file://' != uri.substring(0, 7)) {\n    throw new TypeError('must pass in a file:// URI to convert to a file path');\n  }\n  var rest = decodeURI(uri.substring(7));\n  var firstSlash = rest.indexOf('/');\n  var host = rest.substring(0, firstSlash);\n  var path = rest.substring(firstSlash + 1);\n\n  // 2.  Scheme Definition\n  // As a special case, <host> can be the string \"localhost\" or the empty\n  // string; this is interpreted as \"the machine from which the URL is\n  // being interpreted\".\n  if ('localhost' == host) host = '';\n  if (host) {\n    host = sep + sep + host;\n  }\n\n  // 3.2  Drives, drive letters, mount points, file system root\n  // Drive letters are mapped into the top of a file URI in various ways,\n  // depending on the implementation; some applications substitute\n  // vertical bar (\"|\") for the colon after the drive letter, yielding\n  // \"file:///c|/tmp/test.txt\".  In some cases, the colon is left\n  // unchanged, as in \"file:///c:/tmp/test.txt\".  In other cases, the\n  // colon is simply omitted, as in \"file:///c/tmp/test.txt\".\n  path = path.replace(/^(.+)\\|/, '$1:');\n\n  // for Windows, we need to invert the path separators from what a URI uses\n  if (sep == '\\\\') {\n    path = path.replace(/\\//g, '\\\\');\n  }\n  if (/^.+\\:/.test(path)) {\n    // has Windows drive at beginning of path\n  } else {\n    // unix path…\n    path = sep + path;\n  }\n  return host + path;\n}", "map": {"version": 3, "names": ["sep", "require", "module", "exports", "fileUriToPath", "uri", "length", "substring", "TypeError", "rest", "decodeURI", "firstSlash", "indexOf", "host", "path", "replace", "test"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/file-uri-to-path/index.js"], "sourcesContent": ["\n/**\n * Module dependencies.\n */\n\nvar sep = require('path').sep || '/';\n\n/**\n * Module exports.\n */\n\nmodule.exports = fileUriToPath;\n\n/**\n * File URI to Path function.\n *\n * @param {String} uri\n * @return {String} path\n * @api public\n */\n\nfunction fileUriToPath (uri) {\n  if ('string' != typeof uri ||\n      uri.length <= 7 ||\n      'file://' != uri.substring(0, 7)) {\n    throw new TypeError('must pass in a file:// URI to convert to a file path');\n  }\n\n  var rest = decodeURI(uri.substring(7));\n  var firstSlash = rest.indexOf('/');\n  var host = rest.substring(0, firstSlash);\n  var path = rest.substring(firstSlash + 1);\n\n  // 2.  Scheme Definition\n  // As a special case, <host> can be the string \"localhost\" or the empty\n  // string; this is interpreted as \"the machine from which the URL is\n  // being interpreted\".\n  if ('localhost' == host) host = '';\n\n  if (host) {\n    host = sep + sep + host;\n  }\n\n  // 3.2  Drives, drive letters, mount points, file system root\n  // Drive letters are mapped into the top of a file URI in various ways,\n  // depending on the implementation; some applications substitute\n  // vertical bar (\"|\") for the colon after the drive letter, yielding\n  // \"file:///c|/tmp/test.txt\".  In some cases, the colon is left\n  // unchanged, as in \"file:///c:/tmp/test.txt\".  In other cases, the\n  // colon is simply omitted, as in \"file:///c/tmp/test.txt\".\n  path = path.replace(/^(.+)\\|/, '$1:');\n\n  // for Windows, we need to invert the path separators from what a URI uses\n  if (sep == '\\\\') {\n    path = path.replace(/\\//g, '\\\\');\n  }\n\n  if (/^.+\\:/.test(path)) {\n    // has Windows drive at beginning of path\n  } else {\n    // unix path…\n    path = sep + path;\n  }\n\n  return host + path;\n}\n"], "mappings": "AACA;AACA;AACA;;AAEA,IAAIA,GAAG,GAAGC,OAAO,CAAC,MAAM,CAAC,CAACD,GAAG,IAAI,GAAG;;AAEpC;AACA;AACA;;AAEAE,MAAM,CAACC,OAAO,GAAGC,aAAa;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,aAAaA,CAAEC,GAAG,EAAE;EAC3B,IAAI,QAAQ,IAAI,OAAOA,GAAG,IACtBA,GAAG,CAACC,MAAM,IAAI,CAAC,IACf,SAAS,IAAID,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACpC,MAAM,IAAIC,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIC,IAAI,GAAGC,SAAS,CAACL,GAAG,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;EACtC,IAAII,UAAU,GAAGF,IAAI,CAACG,OAAO,CAAC,GAAG,CAAC;EAClC,IAAIC,IAAI,GAAGJ,IAAI,CAACF,SAAS,CAAC,CAAC,EAAEI,UAAU,CAAC;EACxC,IAAIG,IAAI,GAAGL,IAAI,CAACF,SAAS,CAACI,UAAU,GAAG,CAAC,CAAC;;EAEzC;EACA;EACA;EACA;EACA,IAAI,WAAW,IAAIE,IAAI,EAAEA,IAAI,GAAG,EAAE;EAElC,IAAIA,IAAI,EAAE;IACRA,IAAI,GAAGb,GAAG,GAAGA,GAAG,GAAGa,IAAI;EACzB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;;EAErC;EACA,IAAIf,GAAG,IAAI,IAAI,EAAE;IACfc,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;EAClC;EAEA,IAAI,OAAO,CAACC,IAAI,CAACF,IAAI,CAAC,EAAE;IACtB;EAAA,CACD,MAAM;IACL;IACAA,IAAI,GAAGd,GAAG,GAAGc,IAAI;EACnB;EAEA,OAAOD,IAAI,GAAGC,IAAI;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}