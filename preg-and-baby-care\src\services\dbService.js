import sqliteService from "./sqliteService";

/**
 * Database service for interacting with SQLite
 * This replaces the Supabase implementation with a local SQLite database
 */
const dbService = {
  /**
   * Initialize the database tables if they don't exist
   * This is called when the application starts
   */
  initializeDb: async () => {
    console.log("Database initialized with SQLite");
    // SQLite tables are already created in sqliteService constructor
  },

  /**
   * Get all records from a table
   * @param {string} table - The table name
   * @param {Object} filters - Optional filters to apply
   * @returns {Promise<Array>} - Array of records
   */
  getAll: async (table, filters = {}) => {
    try {
      return sqliteService.getAll(table, filters);
    } catch (error) {
      console.error(`<PERSON>rror fetching all records from ${table}:`, error);
      throw error;
    }
  },

  /**
   * Get a record by ID
   * @param {string} table - The table name
   * @param {number|string} id - The record ID
   * @returns {Promise<Object>} - The record
   */
  getById: async (table, id) => {
    try {
      return sqliteService.getById(table, id);
    } catch (error) {
      console.error(
        `Error fetching record with ID ${id} from ${table}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Find records by a field value
   * @param {string} table - The table name
   * @param {string} field - The field name
   * @param {any} value - The value to match
   * @returns {Promise<Array>} - Array of matching records
   */
  findBy: async (table, field, value) => {
    try {
      return sqliteService.findBy(table, field, value);
    } catch (error) {
      console.error(
        `Error finding records in ${table} where ${field} = ${value}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Create a new record
   * @param {string} table - The table name
   * @param {Object} data - The record data
   * @returns {Promise<Object>} - The created record
   */
  create: async (table, data) => {
    try {
      return sqliteService.create(table, data);
    } catch (error) {
      console.error(`Error creating record in ${table}:`, error);
      throw error;
    }
  },

  /**
   * Update a record
   * @param {string} table - The table name
   * @param {number|string} id - The record ID
   * @param {Object} data - The updated data
   * @returns {Promise<Object>} - The updated record
   */
  update: async (table, id, data) => {
    try {
      return sqliteService.update(table, id, data);
    } catch (error) {
      console.error(`Error updating record with ID ${id} in ${table}:`, error);
      throw error;
    }
  },

  /**
   * Delete a record
   * @param {string} table - The table name
   * @param {number|string} id - The record ID
   * @returns {Promise<boolean>} - Success status
   */
  delete: async (table, id) => {
    try {
      return sqliteService.delete(table, id);
    } catch (error) {
      console.error(
        `Error deleting record with ID ${id} from ${table}:`,
        error
      );
      throw error;
    }
  },
};

export default dbService;
