{"ast": null, "code": "/* eslint no-console: 0 */\nvar isDev = process.env.NODE_ENV !== 'production';\nexport var warn = function warn(condition, format) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  if (isDev && typeof console !== 'undefined' && console.warn) {\n    if (format === undefined) {\n      console.warn('LogUtils requires an error message argument');\n    }\n    if (!condition) {\n      if (format === undefined) {\n        console.warn('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var argIndex = 0;\n        console.warn(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["isDev", "process", "env", "NODE_ENV", "warn", "condition", "format", "_len", "arguments", "length", "args", "Array", "_key", "console", "undefined", "argIndex", "replace"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/recharts/es6/util/LogUtils.js"], "sourcesContent": ["/* eslint no-console: 0 */\nvar isDev = process.env.NODE_ENV !== 'production';\nexport var warn = function warn(condition, format) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  if (isDev && typeof console !== 'undefined' && console.warn) {\n    if (format === undefined) {\n      console.warn('LogUtils requires an error message argument');\n    }\n    if (!condition) {\n      if (format === undefined) {\n        console.warn('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var argIndex = 0;\n        console.warn(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n      }\n    }\n  }\n};"], "mappings": "AAAA;AACA,IAAIA,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACjD,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,SAAS,EAAEC,MAAM,EAAE;EACjD,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAClC;EACA,IAAIZ,KAAK,IAAI,OAAOa,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACT,IAAI,EAAE;IAC3D,IAAIE,MAAM,KAAKQ,SAAS,EAAE;MACxBD,OAAO,CAACT,IAAI,CAAC,6CAA6C,CAAC;IAC7D;IACA,IAAI,CAACC,SAAS,EAAE;MACd,IAAIC,MAAM,KAAKQ,SAAS,EAAE;QACxBD,OAAO,CAACT,IAAI,CAAC,oEAAoE,GAAG,6DAA6D,CAAC;MACpJ,CAAC,MAAM;QACL,IAAIW,QAAQ,GAAG,CAAC;QAChBF,OAAO,CAACT,IAAI,CAACE,MAAM,CAACU,OAAO,CAAC,KAAK,EAAE,YAAY;UAC7C,OAAON,IAAI,CAACK,QAAQ,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;MACL;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}