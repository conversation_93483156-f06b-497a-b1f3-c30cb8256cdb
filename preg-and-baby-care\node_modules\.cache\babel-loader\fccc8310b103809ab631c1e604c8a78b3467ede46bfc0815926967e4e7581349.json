{"ast": null, "code": "'use strict';\n\nexports.getBooleanOption = (options, key) => {\n  let value = false;\n  if (key in options && typeof (value = options[key]) !== 'boolean') {\n    throw new TypeError(`Expected the \"${key}\" option to be a boolean`);\n  }\n  return value;\n};\nexports.cppdb = Symbol();\nexports.inspect = Symbol.for('nodejs.util.inspect.custom');", "map": {"version": 3, "names": ["exports", "getBooleanOption", "options", "key", "value", "TypeError", "cppdb", "Symbol", "inspect", "for"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/better-sqlite3/lib/util.js"], "sourcesContent": ["'use strict';\n\nexports.getBooleanOption = (options, key) => {\n\tlet value = false;\n\tif (key in options && typeof (value = options[key]) !== 'boolean') {\n\t\tthrow new TypeError(`Expected the \"${key}\" option to be a boolean`);\n\t}\n\treturn value;\n};\n\nexports.cppdb = Symbol();\nexports.inspect = Symbol.for('nodejs.util.inspect.custom');\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,gBAAgB,GAAG,CAACC,OAAO,EAAEC,GAAG,KAAK;EAC5C,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAID,GAAG,IAAID,OAAO,IAAI,QAAQE,KAAK,GAAGF,OAAO,CAACC,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;IAClE,MAAM,IAAIE,SAAS,CAAC,iBAAiBF,GAAG,0BAA0B,CAAC;EACpE;EACA,OAAOC,KAAK;AACb,CAAC;AAEDJ,OAAO,CAACM,KAAK,GAAGC,MAAM,CAAC,CAAC;AACxBP,OAAO,CAACQ,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,4BAA4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}