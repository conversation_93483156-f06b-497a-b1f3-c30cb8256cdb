{"ast": null, "code": "var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport isNan from 'lodash/isNaN';\nimport isFunction from 'lodash/isFunction';\nimport omit from 'lodash/omit';\nimport get from 'lodash/get';\nimport clsx from 'clsx';\n/**\n * @fileOverview TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport Smooth from 'react-smooth';\nimport { Tooltip } from '../component/Tooltip';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterSvgElements, findChildByType, validateWidthHeight, filterProps } from '../util/ReactUtils';\nvar NODE_VALUE_KEY = 'value';\nvar computeNode = function computeNode(_ref) {\n  var depth = _ref.depth,\n    node = _ref.node,\n    index = _ref.index,\n    valueKey = _ref.valueKey;\n  var children = node.children;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map(function (child, i) {\n    return computeNode({\n      depth: childDepth,\n      node: child,\n      index: i,\n      valueKey: valueKey\n    });\n  }) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce(function (result, child) {\n      return result + child[NODE_VALUE_KEY];\n    }, 0);\n  } else {\n    // TODO need to verify valueKey\n    nodeValue = isNan(node[valueKey]) || node[valueKey] <= 0 ? 0 : node[valueKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, _defineProperty(_defineProperty(_defineProperty({\n    children: computedChildren\n  }, NODE_VALUE_KEY, nodeValue), \"depth\", depth), \"index\", index));\n};\nvar filterRect = function filterRect(node) {\n  return {\n    x: node.x,\n    y: node.y,\n    width: node.width,\n    height: node.height\n  };\n};\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = function getAreaOfChildren(children, areaValueRatio) {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(function (child) {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: isNan(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = function getWorstScore(row, parentSize, aspectRatio) {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var _row$reduce = row.reduce(function (result, child) {\n      return {\n        min: Math.min(result.min, child.area),\n        max: Math.max(result.max, child.area)\n      };\n    }, {\n      min: Infinity,\n      max: 0\n    }),\n    min = _row$reduce.min,\n    max = _row$reduce.max;\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = function horizontalPosition(row, parentSize, parentRect, isFlush) {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = function verticalPosition(row, parentSize, parentRect, isFlush) {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = function position(row, parentSize, parentRect, isFlush) {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = function squarify(node, aspectRatio) {\n  var children = node.children;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(function (c) {\n        return squarify(c, aspectRatio);\n      })\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isTooltipActive: false,\n  isAnimationFinished: false,\n  activeNode: null,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nexport var Treemap = /*#__PURE__*/function (_PureComponent) {\n  function Treemap() {\n    var _this;\n    _classCallCheck(this, Treemap);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Treemap, [].concat(args));\n    _defineProperty(_this, \"state\", _objectSpread({}, defaultState));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Treemap, _PureComponent);\n  return _createClass(Treemap, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(node, e) {\n      e.persist();\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: true,\n          activeNode: node\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(node, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(node, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(node, e) {\n      e.persist();\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: false,\n          activeNode: null\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(node, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(node, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(node) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        type = _this$props3.type;\n      if (type === 'nest' && node.children) {\n        var _this$props4 = this.props,\n          width = _this$props4.width,\n          height = _this$props4.height,\n          dataKey = _this$props4.dataKey,\n          aspectRatio = _this$props4.aspectRatio;\n        var root = computeNode({\n          depth: 0,\n          node: _objectSpread(_objectSpread({}, node), {}, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }),\n          index: 0,\n          valueKey: dataKey\n        });\n        var formatRoot = squarify(root, aspectRatio);\n        var nestIndex = this.state.nestIndex;\n        nestIndex.push(node);\n        this.setState({\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: nestIndex\n        });\n      }\n      if (onClick) {\n        onClick(node);\n      }\n    }\n  }, {\n    key: \"handleNestIndex\",\n    value: function handleNestIndex(node, i) {\n      var nestIndex = this.state.nestIndex;\n      var _this$props5 = this.props,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        dataKey = _this$props5.dataKey,\n        aspectRatio = _this$props5.aspectRatio;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }),\n        index: 0,\n        valueKey: dataKey\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      nestIndex = nestIndex.slice(0, i + 1);\n      this.setState({\n        formatRoot: formatRoot,\n        currentRoot: node,\n        nestIndex: nestIndex\n      });\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(content, nodeProps, isLeaf) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        isAnimationActive = _this$props6.isAnimationActive,\n        animationBegin = _this$props6.animationBegin,\n        animationDuration = _this$props6.animationDuration,\n        animationEasing = _this$props6.animationEasing,\n        isUpdateAnimationActive = _this$props6.isUpdateAnimationActive,\n        type = _this$props6.type,\n        animationId = _this$props6.animationId,\n        colorPanel = _this$props6.colorPanel;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var width = nodeProps.width,\n        height = nodeProps.height,\n        x = nodeProps.x,\n        y = nodeProps.y,\n        depth = nodeProps.depth;\n      var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n      var event = {};\n      if (isLeaf || type === 'nest') {\n        event = {\n          onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n          onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n          onClick: this.handleClick.bind(this, nodeProps)\n        };\n      }\n      if (!isAnimationActive) {\n        return /*#__PURE__*/React.createElement(Layer, event, this.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width: width,\n          height: height,\n          x: x,\n          y: y\n        }), type, colorPanel));\n      }\n      return /*#__PURE__*/React.createElement(Smooth, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        key: \"treemap-\".concat(animationId),\n        from: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        to: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var currX = _ref2.x,\n          currY = _ref2.y,\n          currWidth = _ref2.width,\n          currHeight = _ref2.height;\n        return /*#__PURE__*/React.createElement(Smooth, {\n          from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\"),\n          to: \"translate(0, 0)\",\n          attributeName: \"transform\",\n          begin: animationBegin,\n          easing: animationEasing,\n          isActive: isAnimationActive,\n          duration: animationDuration\n        }, /*#__PURE__*/React.createElement(Layer, event, function () {\n          // when animation Duration , only render depth=1 nodes\n          if (depth > 2 && !isAnimationFinished) {\n            return null;\n          }\n          return _this2.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n            isAnimationActive: isAnimationActive,\n            isUpdateAnimationActive: !isUpdateAnimationActive,\n            width: currWidth,\n            height: currHeight,\n            x: currX,\n            y: currY\n          }), type, colorPanel);\n        }()));\n      });\n    }\n  }, {\n    key: \"renderNode\",\n    value: function renderNode(root, node) {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        content = _this$props7.content,\n        type = _this$props7.type;\n      var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), node), {}, {\n        root: root\n      });\n      var isLeaf = !node.children || !node.children.length;\n      var currentRoot = this.state.currentRoot;\n      var isCurrentRootChild = (currentRoot.children || []).filter(function (item) {\n        return item.depth === node.depth && item.name === node.name;\n      });\n      if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-treemap-node-\".concat(nodeProps.x, \"-\").concat(nodeProps.y, \"-\").concat(nodeProps.name),\n        className: \"recharts-treemap-depth-\".concat(node.depth)\n      }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(function (child) {\n        return _this3.renderNode(node, child);\n      }) : null);\n    }\n  }, {\n    key: \"renderAllNodes\",\n    value: function renderAllNodes() {\n      var formatRoot = this.state.formatRoot;\n      if (!formatRoot) {\n        return null;\n      }\n      return this.renderNode(formatRoot, formatRoot);\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props8 = this.props,\n        children = _this$props8.children,\n        nameKey = _this$props8.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$props9 = this.props,\n        width = _this$props9.width,\n        height = _this$props9.height;\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeNode = _this$state.activeNode;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeNode ? {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      } : null;\n      var payload = isTooltipActive && activeNode ? [{\n        payload: activeNode,\n        name: getValueByDataKey(activeNode, nameKey, ''),\n        value: getValueByDataKey(activeNode, NODE_VALUE_KEY)\n      }] : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n\n    // render nest treemap\n  }, {\n    key: \"renderNestIndex\",\n    value: function renderNestIndex() {\n      var _this4 = this;\n      var _this$props10 = this.props,\n        nameKey = _this$props10.nameKey,\n        nestIndexContent = _this$props10.nestIndexContent;\n      var nestIndex = this.state.nestIndex;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-treemap-nest-index-wrapper\",\n        style: {\n          marginTop: '8px',\n          textAlign: 'center'\n        }\n      }, nestIndex.map(function (item, i) {\n        // TODO need to verify nameKey type\n        var name = get(item, nameKey, 'root');\n        var content = null;\n        if (/*#__PURE__*/React.isValidElement(nestIndexContent)) {\n          content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n        }\n        if (isFunction(nestIndexContent)) {\n          content = nestIndexContent(item, i);\n        } else {\n          content = name;\n        }\n        return (/*#__PURE__*/\n          // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n          React.createElement(\"div\", {\n            onClick: _this4.handleNestIndex.bind(_this4, item, i),\n            key: \"nest-index-\".concat(uniqueId()),\n            className: \"recharts-treemap-nest-index-box\",\n            style: {\n              cursor: 'pointer',\n              display: 'inline-block',\n              padding: '0 7px',\n              background: '#000',\n              color: '#fff',\n              marginRight: '3px'\n            }\n          }, content)\n        );\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props11 = this.props,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        className = _this$props11.className,\n        style = _this$props11.style,\n        children = _this$props11.children,\n        type = _this$props11.type,\n        others = _objectWithoutProperties(_this$props11, _excluded);\n      var attrs = filterProps(others, false);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: type === 'nest' ? height - 30 : height\n      }), this.renderAllNodes(), filterSvgElements(children)), this.renderTooltip(), type === 'nest' && this.renderNestIndex());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n        var root = computeNode({\n          depth: 0,\n          node: {\n            children: nextProps.data,\n            x: 0,\n            y: 0,\n            width: nextProps.width,\n            height: nextProps.height\n          },\n          index: 0,\n          valueKey: nextProps.dataKey\n        });\n        var formatRoot = squarify(root, nextProps.aspectRatio);\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: [root],\n          prevAspectRatio: nextProps.aspectRatio,\n          prevData: nextProps.data,\n          prevWidth: nextProps.width,\n          prevHeight: nextProps.height,\n          prevDataKey: nextProps.dataKey,\n          prevType: nextProps.type\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContentItem\",\n    value: function renderContentItem(content, nodeProps, type, colorPanel) {\n      if (/*#__PURE__*/React.isValidElement(content)) {\n        return /*#__PURE__*/React.cloneElement(content, nodeProps);\n      }\n      if (isFunction(content)) {\n        return content(nodeProps);\n      }\n      // optimize default shape\n      var x = nodeProps.x,\n        y = nodeProps.y,\n        width = nodeProps.width,\n        height = nodeProps.height,\n        index = nodeProps.index;\n      var arrow = null;\n      if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n        arrow = /*#__PURE__*/React.createElement(Polygon, {\n          points: [{\n            x: x + 2,\n            y: y + height / 2\n          }, {\n            x: x + 6,\n            y: y + height / 2 + 3\n          }, {\n            x: x + 2,\n            y: y + height / 2 + 6\n          }]\n        });\n      }\n      var text = null;\n      var nameSize = getStringSize(nodeProps.name);\n      if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n        text = /*#__PURE__*/React.createElement(\"text\", {\n          x: x + 8,\n          y: y + height / 2 + 7,\n          fontSize: 14\n        }, nodeProps.name);\n      }\n      var colors = colorPanel || COLOR_PANEL;\n      return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n        fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n        stroke: \"#fff\"\n      }, omit(nodeProps, 'children'), {\n        role: \"img\"\n      })), arrow, text);\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Treemap, \"displayName\", 'Treemap');\n_defineProperty(Treemap, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});", "map": {"version": 3, "names": ["_excluded", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "t", "e", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "ownKeys", "r", "keys", "filter", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "isNan", "isFunction", "omit", "get", "clsx", "React", "PureComponent", "Smooth", "<PERSON><PERSON><PERSON>", "Layer", "Surface", "Polygon", "Rectangle", "getValueByDataKey", "COLOR_PANEL", "uniqueId", "getStringSize", "Global", "filterSvgElements", "findChildByType", "validateWidthHeight", "filterProps", "NODE_VALUE_KEY", "computeNode", "_ref", "depth", "node", "index", "valueKey", "children", "<PERSON><PERSON><PERSON><PERSON>", "computed<PERSON><PERSON><PERSON>n", "map", "child", "nodeValue", "reduce", "result", "filterRect", "x", "y", "width", "height", "getAreaOfChildren", "areaValueRatio", "ratio", "area", "getWorstScore", "row", "parentSize", "aspectRatio", "parentArea", "rowArea", "_row$reduce", "min", "Math", "max", "Infinity", "horizontalPosition", "parentRect", "isFlush", "rowHeight", "round", "curX", "len", "verticalPosition", "row<PERSON>id<PERSON>", "curY", "position", "squarify", "rect", "best", "score", "size", "scaleChildren", "tempC<PERSON><PERSON>n", "slice", "shift", "pop", "c", "defaultState", "isTooltipActive", "isAnimationFinished", "activeNode", "formatRoot", "currentRoot", "nestIndex", "Treemap", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "onAnimationEnd", "setState", "onAnimationStart", "handleMouseEnter", "persist", "_this$props", "onMouseEnter", "tooltipItem", "handleMouseLeave", "_this$props2", "onMouseLeave", "handleClick", "_this$props3", "onClick", "type", "_this$props4", "dataKey", "root", "state", "handleNestIndex", "_this$props5", "renderItem", "content", "nodeProps", "<PERSON><PERSON><PERSON><PERSON>", "_this2", "_this$props6", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "isUpdateAnimationActive", "animationId", "colorPanel", "translateX", "parseInt", "random", "event", "createElement", "renderContentItem", "begin", "duration", "isActive", "easing", "from", "to", "handleAnimationStart", "handleAnimationEnd", "_ref2", "currX", "currY", "currWidth", "currHeight", "attributeName", "renderNode", "_this3", "_this$props7", "isCurrentRootChild", "item", "name", "className", "renderAllNodes", "renderTooltip", "_this$props8", "<PERSON><PERSON><PERSON>", "_this$props9", "_this$state", "viewBox", "coordinate", "payload", "cloneElement", "active", "label", "renderNestIndex", "_this4", "_this$props10", "nestIndexContent", "style", "marginTop", "textAlign", "isValidElement", "cursor", "display", "padding", "background", "color", "marginRight", "render", "_this$props11", "others", "attrs", "role", "getDerivedStateFromProps", "nextProps", "prevState", "data", "prevData", "prevType", "prevWidth", "prevHeight", "prevDataKey", "prevAspectRatio", "arrow", "points", "text", "nameSize", "fontSize", "colors", "fill", "stroke", "sqrt", "isSsr"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/recharts/es6/chart/Treemap.js"], "sourcesContent": ["var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport isNan from 'lodash/isNaN';\nimport isFunction from 'lodash/isFunction';\nimport omit from 'lodash/omit';\nimport get from 'lodash/get';\nimport clsx from 'clsx';\n/**\n * @fileOverview TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport Smooth from 'react-smooth';\nimport { Tooltip } from '../component/Tooltip';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterSvgElements, findChildByType, validateWidthHeight, filterProps } from '../util/ReactUtils';\nvar NODE_VALUE_KEY = 'value';\nvar computeNode = function computeNode(_ref) {\n  var depth = _ref.depth,\n    node = _ref.node,\n    index = _ref.index,\n    valueKey = _ref.valueKey;\n  var children = node.children;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map(function (child, i) {\n    return computeNode({\n      depth: childDepth,\n      node: child,\n      index: i,\n      valueKey: valueKey\n    });\n  }) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce(function (result, child) {\n      return result + child[NODE_VALUE_KEY];\n    }, 0);\n  } else {\n    // TODO need to verify valueKey\n    nodeValue = isNan(node[valueKey]) || node[valueKey] <= 0 ? 0 : node[valueKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, _defineProperty(_defineProperty(_defineProperty({\n    children: computedChildren\n  }, NODE_VALUE_KEY, nodeValue), \"depth\", depth), \"index\", index));\n};\nvar filterRect = function filterRect(node) {\n  return {\n    x: node.x,\n    y: node.y,\n    width: node.width,\n    height: node.height\n  };\n};\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = function getAreaOfChildren(children, areaValueRatio) {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(function (child) {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: isNan(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = function getWorstScore(row, parentSize, aspectRatio) {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var _row$reduce = row.reduce(function (result, child) {\n      return {\n        min: Math.min(result.min, child.area),\n        max: Math.max(result.max, child.area)\n      };\n    }, {\n      min: Infinity,\n      max: 0\n    }),\n    min = _row$reduce.min,\n    max = _row$reduce.max;\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = function horizontalPosition(row, parentSize, parentRect, isFlush) {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = function verticalPosition(row, parentSize, parentRect, isFlush) {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = function position(row, parentSize, parentRect, isFlush) {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = function squarify(node, aspectRatio) {\n  var children = node.children;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(function (c) {\n        return squarify(c, aspectRatio);\n      })\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isTooltipActive: false,\n  isAnimationFinished: false,\n  activeNode: null,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nexport var Treemap = /*#__PURE__*/function (_PureComponent) {\n  function Treemap() {\n    var _this;\n    _classCallCheck(this, Treemap);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Treemap, [].concat(args));\n    _defineProperty(_this, \"state\", _objectSpread({}, defaultState));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Treemap, _PureComponent);\n  return _createClass(Treemap, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(node, e) {\n      e.persist();\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: true,\n          activeNode: node\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(node, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(node, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(node, e) {\n      e.persist();\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: false,\n          activeNode: null\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(node, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(node, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(node) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        type = _this$props3.type;\n      if (type === 'nest' && node.children) {\n        var _this$props4 = this.props,\n          width = _this$props4.width,\n          height = _this$props4.height,\n          dataKey = _this$props4.dataKey,\n          aspectRatio = _this$props4.aspectRatio;\n        var root = computeNode({\n          depth: 0,\n          node: _objectSpread(_objectSpread({}, node), {}, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }),\n          index: 0,\n          valueKey: dataKey\n        });\n        var formatRoot = squarify(root, aspectRatio);\n        var nestIndex = this.state.nestIndex;\n        nestIndex.push(node);\n        this.setState({\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: nestIndex\n        });\n      }\n      if (onClick) {\n        onClick(node);\n      }\n    }\n  }, {\n    key: \"handleNestIndex\",\n    value: function handleNestIndex(node, i) {\n      var nestIndex = this.state.nestIndex;\n      var _this$props5 = this.props,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        dataKey = _this$props5.dataKey,\n        aspectRatio = _this$props5.aspectRatio;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }),\n        index: 0,\n        valueKey: dataKey\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      nestIndex = nestIndex.slice(0, i + 1);\n      this.setState({\n        formatRoot: formatRoot,\n        currentRoot: node,\n        nestIndex: nestIndex\n      });\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(content, nodeProps, isLeaf) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        isAnimationActive = _this$props6.isAnimationActive,\n        animationBegin = _this$props6.animationBegin,\n        animationDuration = _this$props6.animationDuration,\n        animationEasing = _this$props6.animationEasing,\n        isUpdateAnimationActive = _this$props6.isUpdateAnimationActive,\n        type = _this$props6.type,\n        animationId = _this$props6.animationId,\n        colorPanel = _this$props6.colorPanel;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var width = nodeProps.width,\n        height = nodeProps.height,\n        x = nodeProps.x,\n        y = nodeProps.y,\n        depth = nodeProps.depth;\n      var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n      var event = {};\n      if (isLeaf || type === 'nest') {\n        event = {\n          onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n          onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n          onClick: this.handleClick.bind(this, nodeProps)\n        };\n      }\n      if (!isAnimationActive) {\n        return /*#__PURE__*/React.createElement(Layer, event, this.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width: width,\n          height: height,\n          x: x,\n          y: y\n        }), type, colorPanel));\n      }\n      return /*#__PURE__*/React.createElement(Smooth, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        key: \"treemap-\".concat(animationId),\n        from: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        to: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var currX = _ref2.x,\n          currY = _ref2.y,\n          currWidth = _ref2.width,\n          currHeight = _ref2.height;\n        return /*#__PURE__*/React.createElement(Smooth, {\n          from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\"),\n          to: \"translate(0, 0)\",\n          attributeName: \"transform\",\n          begin: animationBegin,\n          easing: animationEasing,\n          isActive: isAnimationActive,\n          duration: animationDuration\n        }, /*#__PURE__*/React.createElement(Layer, event, function () {\n          // when animation Duration , only render depth=1 nodes\n          if (depth > 2 && !isAnimationFinished) {\n            return null;\n          }\n          return _this2.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n            isAnimationActive: isAnimationActive,\n            isUpdateAnimationActive: !isUpdateAnimationActive,\n            width: currWidth,\n            height: currHeight,\n            x: currX,\n            y: currY\n          }), type, colorPanel);\n        }()));\n      });\n    }\n  }, {\n    key: \"renderNode\",\n    value: function renderNode(root, node) {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        content = _this$props7.content,\n        type = _this$props7.type;\n      var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), node), {}, {\n        root: root\n      });\n      var isLeaf = !node.children || !node.children.length;\n      var currentRoot = this.state.currentRoot;\n      var isCurrentRootChild = (currentRoot.children || []).filter(function (item) {\n        return item.depth === node.depth && item.name === node.name;\n      });\n      if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-treemap-node-\".concat(nodeProps.x, \"-\").concat(nodeProps.y, \"-\").concat(nodeProps.name),\n        className: \"recharts-treemap-depth-\".concat(node.depth)\n      }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(function (child) {\n        return _this3.renderNode(node, child);\n      }) : null);\n    }\n  }, {\n    key: \"renderAllNodes\",\n    value: function renderAllNodes() {\n      var formatRoot = this.state.formatRoot;\n      if (!formatRoot) {\n        return null;\n      }\n      return this.renderNode(formatRoot, formatRoot);\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props8 = this.props,\n        children = _this$props8.children,\n        nameKey = _this$props8.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$props9 = this.props,\n        width = _this$props9.width,\n        height = _this$props9.height;\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeNode = _this$state.activeNode;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeNode ? {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      } : null;\n      var payload = isTooltipActive && activeNode ? [{\n        payload: activeNode,\n        name: getValueByDataKey(activeNode, nameKey, ''),\n        value: getValueByDataKey(activeNode, NODE_VALUE_KEY)\n      }] : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n\n    // render nest treemap\n  }, {\n    key: \"renderNestIndex\",\n    value: function renderNestIndex() {\n      var _this4 = this;\n      var _this$props10 = this.props,\n        nameKey = _this$props10.nameKey,\n        nestIndexContent = _this$props10.nestIndexContent;\n      var nestIndex = this.state.nestIndex;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-treemap-nest-index-wrapper\",\n        style: {\n          marginTop: '8px',\n          textAlign: 'center'\n        }\n      }, nestIndex.map(function (item, i) {\n        // TODO need to verify nameKey type\n        var name = get(item, nameKey, 'root');\n        var content = null;\n        if ( /*#__PURE__*/React.isValidElement(nestIndexContent)) {\n          content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n        }\n        if (isFunction(nestIndexContent)) {\n          content = nestIndexContent(item, i);\n        } else {\n          content = name;\n        }\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n          React.createElement(\"div\", {\n            onClick: _this4.handleNestIndex.bind(_this4, item, i),\n            key: \"nest-index-\".concat(uniqueId()),\n            className: \"recharts-treemap-nest-index-box\",\n            style: {\n              cursor: 'pointer',\n              display: 'inline-block',\n              padding: '0 7px',\n              background: '#000',\n              color: '#fff',\n              marginRight: '3px'\n            }\n          }, content)\n        );\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props11 = this.props,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        className = _this$props11.className,\n        style = _this$props11.style,\n        children = _this$props11.children,\n        type = _this$props11.type,\n        others = _objectWithoutProperties(_this$props11, _excluded);\n      var attrs = filterProps(others, false);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: type === 'nest' ? height - 30 : height\n      }), this.renderAllNodes(), filterSvgElements(children)), this.renderTooltip(), type === 'nest' && this.renderNestIndex());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n        var root = computeNode({\n          depth: 0,\n          node: {\n            children: nextProps.data,\n            x: 0,\n            y: 0,\n            width: nextProps.width,\n            height: nextProps.height\n          },\n          index: 0,\n          valueKey: nextProps.dataKey\n        });\n        var formatRoot = squarify(root, nextProps.aspectRatio);\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: [root],\n          prevAspectRatio: nextProps.aspectRatio,\n          prevData: nextProps.data,\n          prevWidth: nextProps.width,\n          prevHeight: nextProps.height,\n          prevDataKey: nextProps.dataKey,\n          prevType: nextProps.type\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContentItem\",\n    value: function renderContentItem(content, nodeProps, type, colorPanel) {\n      if ( /*#__PURE__*/React.isValidElement(content)) {\n        return /*#__PURE__*/React.cloneElement(content, nodeProps);\n      }\n      if (isFunction(content)) {\n        return content(nodeProps);\n      }\n      // optimize default shape\n      var x = nodeProps.x,\n        y = nodeProps.y,\n        width = nodeProps.width,\n        height = nodeProps.height,\n        index = nodeProps.index;\n      var arrow = null;\n      if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n        arrow = /*#__PURE__*/React.createElement(Polygon, {\n          points: [{\n            x: x + 2,\n            y: y + height / 2\n          }, {\n            x: x + 6,\n            y: y + height / 2 + 3\n          }, {\n            x: x + 2,\n            y: y + height / 2 + 6\n          }]\n        });\n      }\n      var text = null;\n      var nameSize = getStringSize(nodeProps.name);\n      if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n        text = /*#__PURE__*/React.createElement(\"text\", {\n          x: x + 8,\n          y: y + height / 2 + 7,\n          fontSize: 14\n        }, nodeProps.name);\n      }\n      var colors = colorPanel || COLOR_PANEL;\n      return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n        fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n        stroke: \"#fff\"\n      }, omit(nodeProps, 'children'), {\n        role: \"img\"\n      })), arrow, text);\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Treemap, \"displayName\", 'Treemap');\n_defineProperty(Treemap, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;AAC7E,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,wBAAwBA,CAACL,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGW,6BAA6B,CAACP,MAAM,EAAEM,QAAQ,CAAC;EAAE,IAAIL,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGhB,MAAM,CAACe,qBAAqB,CAACR,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,gBAAgB,CAACV,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGQ,gBAAgB,CAACZ,CAAC,CAAC;MAAE,IAAIS,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACoB,oBAAoB,CAACR,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASW,6BAA6BA,CAACP,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAIK,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,SAASgB,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACpB,MAAM,EAAEqB,KAAK,EAAE;EAAE,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,KAAK,CAAClB,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIqB,UAAU,GAAGD,KAAK,CAACpB,CAAC,CAAC;IAAEqB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAE5B,MAAM,CAAC6B,cAAc,CAAC1B,MAAM,EAAE2B,cAAc,CAACL,UAAU,CAACjB,GAAG,CAAC,EAAEiB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACF,WAAW,CAACvB,SAAS,EAAEkC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACF,WAAW,EAAEY,WAAW,CAAC;EAAEjC,MAAM,CAAC6B,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASa,UAAUA,CAACC,CAAC,EAAEzC,CAAC,EAAE0C,CAAC,EAAE;EAAE,OAAO1C,CAAC,GAAG2C,eAAe,CAAC3C,CAAC,CAAC,EAAE4C,0BAA0B,CAACH,CAAC,EAAEI,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC/C,CAAC,EAAE0C,CAAC,IAAI,EAAE,EAAEC,eAAe,CAACF,CAAC,CAAC,CAACtC,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACwB,CAAC,EAAEC,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASE,0BAA0BA,CAACI,IAAI,EAAEhC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIY,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOqB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIJ,CAAC,GAAG,CAACU,OAAO,CAAC/C,SAAS,CAACgD,OAAO,CAACpC,IAAI,CAAC8B,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOV,CAAC,EAAE,CAAC;EAAE,OAAO,CAACI,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACJ,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASE,eAAeA,CAAC3C,CAAC,EAAE;EAAE2C,eAAe,GAAGrC,MAAM,CAAC+C,cAAc,GAAG/C,MAAM,CAACgD,cAAc,CAAC9C,IAAI,CAAC,CAAC,GAAG,SAASmC,eAAeA,CAAC3C,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACuD,SAAS,IAAIjD,MAAM,CAACgD,cAAc,CAACtD,CAAC,CAAC;EAAE,CAAC;EAAE,OAAO2C,eAAe,CAAC3C,CAAC,CAAC;AAAE;AACnN,SAASwD,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI9B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAE6B,QAAQ,CAACrD,SAAS,GAAGE,MAAM,CAACqD,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACtD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEyD,KAAK,EAAEH,QAAQ;MAAEvB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE3B,MAAM,CAAC6B,cAAc,CAACsB,QAAQ,EAAE,WAAW,EAAE;IAAEvB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIwB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAC7D,CAAC,EAAE8D,CAAC,EAAE;EAAED,eAAe,GAAGvD,MAAM,CAAC+C,cAAc,GAAG/C,MAAM,CAAC+C,cAAc,CAAC7C,IAAI,CAAC,CAAC,GAAG,SAASqD,eAAeA,CAAC7D,CAAC,EAAE8D,CAAC,EAAE;IAAE9D,CAAC,CAACuD,SAAS,GAAGO,CAAC;IAAE,OAAO9D,CAAC;EAAE,CAAC;EAAE,OAAO6D,eAAe,CAAC7D,CAAC,EAAE8D,CAAC,CAAC;AAAE;AACvM,SAASC,OAAOA,CAACrB,CAAC,EAAEsB,CAAC,EAAE;EAAE,IAAIvB,CAAC,GAAGnC,MAAM,CAAC2D,IAAI,CAACvB,CAAC,CAAC;EAAE,IAAIpC,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIrB,CAAC,GAAGM,MAAM,CAACe,qBAAqB,CAACqB,CAAC,CAAC;IAAEsB,CAAC,KAAKhE,CAAC,GAAGA,CAAC,CAACkE,MAAM,CAAC,UAAUF,CAAC,EAAE;MAAE,OAAO1D,MAAM,CAAC6D,wBAAwB,CAACzB,CAAC,EAAEsB,CAAC,CAAC,CAAChC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAES,CAAC,CAAC2B,IAAI,CAACnD,KAAK,CAACwB,CAAC,EAAEzC,CAAC,CAAC;EAAE;EAAE,OAAOyC,CAAC;AAAE;AAC9P,SAAS4B,aAAaA,CAAC3B,CAAC,EAAE;EAAE,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,SAAS,CAACC,MAAM,EAAEoD,CAAC,EAAE,EAAE;IAAE,IAAIvB,CAAC,GAAG,IAAI,IAAI9B,SAAS,CAACqD,CAAC,CAAC,GAAGrD,SAAS,CAACqD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGD,OAAO,CAACzD,MAAM,CAACmC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC6B,OAAO,CAAC,UAAUN,CAAC,EAAE;MAAEO,eAAe,CAAC7B,CAAC,EAAEsB,CAAC,EAAEvB,CAAC,CAACuB,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAG1D,MAAM,CAACkE,yBAAyB,GAAGlE,MAAM,CAACmE,gBAAgB,CAAC/B,CAAC,EAAEpC,MAAM,CAACkE,yBAAyB,CAAC/B,CAAC,CAAC,CAAC,GAAGsB,OAAO,CAACzD,MAAM,CAACmC,CAAC,CAAC,CAAC,CAAC6B,OAAO,CAAC,UAAUN,CAAC,EAAE;MAAE1D,MAAM,CAAC6B,cAAc,CAACO,CAAC,EAAEsB,CAAC,EAAE1D,MAAM,CAAC6D,wBAAwB,CAAC1B,CAAC,EAAEuB,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOtB,CAAC;AAAE;AACtb,SAAS6B,eAAeA,CAACG,GAAG,EAAE5D,GAAG,EAAE8C,KAAK,EAAE;EAAE9C,GAAG,GAAGsB,cAAc,CAACtB,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI4D,GAAG,EAAE;IAAEpE,MAAM,CAAC6B,cAAc,CAACuC,GAAG,EAAE5D,GAAG,EAAE;MAAE8C,KAAK,EAAEA,KAAK;MAAE5B,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEwC,GAAG,CAAC5D,GAAG,CAAC,GAAG8C,KAAK;EAAE;EAAE,OAAOc,GAAG;AAAE;AAC3O,SAAStC,cAAcA,CAACK,CAAC,EAAE;EAAE,IAAI/B,CAAC,GAAGiE,YAAY,CAAClC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI1C,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASiE,YAAYA,CAAClC,CAAC,EAAEuB,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIjE,OAAO,CAAC0C,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAACxC,MAAM,CAAC2E,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAIhC,CAAC,GAAGgC,CAAC,CAAC1B,IAAI,CAACyB,CAAC,EAAEuB,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIjE,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIkB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKoC,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAErC,CAAC,CAAC;AAAE;AAC3T,OAAOsC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,IAAI,MAAM,MAAM;AACvB;AACA;AACA;AACA,OAAOC,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,oBAAoB;AACzG,IAAIC,cAAc,GAAG,OAAO;AAC5B,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC1B,IAAIC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,UAAU,GAAGL,KAAK,GAAG,CAAC;EAC1B,IAAIM,gBAAgB,GAAGF,QAAQ,IAAIA,QAAQ,CAAChG,MAAM,GAAGgG,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAEtG,CAAC,EAAE;IACpF,OAAO4F,WAAW,CAAC;MACjBE,KAAK,EAAEK,UAAU;MACjBJ,IAAI,EAAEO,KAAK;MACXN,KAAK,EAAEhG,CAAC;MACRiG,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,GAAG,IAAI;EACT,IAAIM,SAAS;EACb,IAAIL,QAAQ,IAAIA,QAAQ,CAAChG,MAAM,EAAE;IAC/BqG,SAAS,GAAGH,gBAAgB,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEH,KAAK,EAAE;MAC3D,OAAOG,MAAM,GAAGH,KAAK,CAACX,cAAc,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,MAAM;IACL;IACAY,SAAS,GAAGlC,KAAK,CAAC0B,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACE,QAAQ,CAAC;EAC/E;EACA,OAAOtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAElC,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC;IAChGqC,QAAQ,EAAEE;EACZ,CAAC,EAAET,cAAc,EAAEY,SAAS,CAAC,EAAE,OAAO,EAAET,KAAK,CAAC,EAAE,OAAO,EAAEE,KAAK,CAAC,CAAC;AAClE,CAAC;AACD,IAAIU,UAAU,GAAG,SAASA,UAAUA,CAACX,IAAI,EAAE;EACzC,OAAO;IACLY,CAAC,EAAEZ,IAAI,CAACY,CAAC;IACTC,CAAC,EAAEb,IAAI,CAACa,CAAC;IACTC,KAAK,EAAEd,IAAI,CAACc,KAAK;IACjBC,MAAM,EAAEf,IAAI,CAACe;EACf,CAAC;AACH,CAAC;;AAED;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACb,QAAQ,EAAEc,cAAc,EAAE;EAC3E,IAAIC,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc;EACnD,OAAOd,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;IACnC,IAAIY,IAAI,GAAGZ,KAAK,CAACX,cAAc,CAAC,GAAGsB,KAAK;IACxC,OAAOtD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDY,IAAI,EAAE7C,KAAK,CAAC6C,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACvE,IAAIC,UAAU,GAAGF,UAAU,GAAGA,UAAU;EACxC,IAAIG,OAAO,GAAGJ,GAAG,CAACF,IAAI,GAAGE,GAAG,CAACF,IAAI;EACjC,IAAIO,WAAW,GAAGL,GAAG,CAACZ,MAAM,CAAC,UAAUC,MAAM,EAAEH,KAAK,EAAE;MAClD,OAAO;QACLoB,GAAG,EAAEC,IAAI,CAACD,GAAG,CAACjB,MAAM,CAACiB,GAAG,EAAEpB,KAAK,CAACY,IAAI,CAAC;QACrCU,GAAG,EAAED,IAAI,CAACC,GAAG,CAACnB,MAAM,CAACmB,GAAG,EAAEtB,KAAK,CAACY,IAAI;MACtC,CAAC;IACH,CAAC,EAAE;MACDQ,GAAG,EAAEG,QAAQ;MACbD,GAAG,EAAE;IACP,CAAC,CAAC;IACFF,GAAG,GAAGD,WAAW,CAACC,GAAG;IACrBE,GAAG,GAAGH,WAAW,CAACG,GAAG;EACvB,OAAOJ,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAGN,WAAW,GAAGE,OAAO,EAAEA,OAAO,IAAID,UAAU,GAAGG,GAAG,GAAGJ,WAAW,CAAC,CAAC,GAAGO,QAAQ;AAC5H,CAAC;AACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACV,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACzF,IAAIC,SAAS,GAAGZ,UAAU,GAAGM,IAAI,CAACO,KAAK,CAACd,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EAClE,IAAIW,OAAO,IAAIC,SAAS,GAAGF,UAAU,CAACjB,MAAM,EAAE;IAC5CmB,SAAS,GAAGF,UAAU,CAACjB,MAAM;EAC/B;EACA,IAAIqB,IAAI,GAAGJ,UAAU,CAACpB,CAAC;EACvB,IAAIL,KAAK;EACT,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEoI,GAAG,GAAGhB,GAAG,CAAClH,MAAM,EAAEF,CAAC,GAAGoI,GAAG,EAAEpI,CAAC,EAAE,EAAE;IAC9CsG,KAAK,GAAGc,GAAG,CAACpH,CAAC,CAAC;IACdsG,KAAK,CAACK,CAAC,GAAGwB,IAAI;IACd7B,KAAK,CAACM,CAAC,GAAGmB,UAAU,CAACnB,CAAC;IACtBN,KAAK,CAACQ,MAAM,GAAGmB,SAAS;IACxB3B,KAAK,CAACO,KAAK,GAAGc,IAAI,CAACD,GAAG,CAACO,SAAS,GAAGN,IAAI,CAACO,KAAK,CAAC5B,KAAK,CAACY,IAAI,GAAGe,SAAS,CAAC,GAAG,CAAC,EAAEF,UAAU,CAACpB,CAAC,GAAGoB,UAAU,CAAClB,KAAK,GAAGsB,IAAI,CAAC;IAClHA,IAAI,IAAI7B,KAAK,CAACO,KAAK;EACrB;EACA;EACAP,KAAK,CAACO,KAAK,IAAIkB,UAAU,CAACpB,CAAC,GAAGoB,UAAU,CAAClB,KAAK,GAAGsB,IAAI;EACrD,OAAOxE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDnB,CAAC,EAAEmB,UAAU,CAACnB,CAAC,GAAGqB,SAAS;IAC3BnB,MAAM,EAAEiB,UAAU,CAACjB,MAAM,GAAGmB;EAC9B,CAAC,CAAC;AACJ,CAAC;AACD,IAAII,gBAAgB,GAAG,SAASA,gBAAgBA,CAACjB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACrF,IAAIM,QAAQ,GAAGjB,UAAU,GAAGM,IAAI,CAACO,KAAK,CAACd,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EACjE,IAAIW,OAAO,IAAIM,QAAQ,GAAGP,UAAU,CAAClB,KAAK,EAAE;IAC1CyB,QAAQ,GAAGP,UAAU,CAAClB,KAAK;EAC7B;EACA,IAAI0B,IAAI,GAAGR,UAAU,CAACnB,CAAC;EACvB,IAAIN,KAAK;EACT,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEoI,GAAG,GAAGhB,GAAG,CAAClH,MAAM,EAAEF,CAAC,GAAGoI,GAAG,EAAEpI,CAAC,EAAE,EAAE;IAC9CsG,KAAK,GAAGc,GAAG,CAACpH,CAAC,CAAC;IACdsG,KAAK,CAACK,CAAC,GAAGoB,UAAU,CAACpB,CAAC;IACtBL,KAAK,CAACM,CAAC,GAAG2B,IAAI;IACdjC,KAAK,CAACO,KAAK,GAAGyB,QAAQ;IACtBhC,KAAK,CAACQ,MAAM,GAAGa,IAAI,CAACD,GAAG,CAACY,QAAQ,GAAGX,IAAI,CAACO,KAAK,CAAC5B,KAAK,CAACY,IAAI,GAAGoB,QAAQ,CAAC,GAAG,CAAC,EAAEP,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,MAAM,GAAGyB,IAAI,CAAC;IAClHA,IAAI,IAAIjC,KAAK,CAACQ,MAAM;EACtB;EACA,IAAIR,KAAK,EAAE;IACTA,KAAK,CAACQ,MAAM,IAAIiB,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,MAAM,GAAGyB,IAAI;EACzD;EACA,OAAO5E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDpB,CAAC,EAAEoB,UAAU,CAACpB,CAAC,GAAG2B,QAAQ;IAC1BzB,KAAK,EAAEkB,UAAU,CAAClB,KAAK,GAAGyB;EAC5B,CAAC,CAAC;AACJ,CAAC;AACD,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACpB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACrE,IAAIX,UAAU,KAAKU,UAAU,CAAClB,KAAK,EAAE;IACnC,OAAOiB,kBAAkB,CAACV,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,CAAC;EACjE;EACA,OAAOK,gBAAgB,CAACjB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,CAAC;AAC/D,CAAC;;AAED;AACA,IAAIS,QAAQ,GAAG,SAASA,QAAQA,CAAC1C,IAAI,EAAEuB,WAAW,EAAE;EAClD,IAAIpB,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAChG,MAAM,EAAE;IAC/B,IAAIwI,IAAI,GAAGhC,UAAU,CAACX,IAAI,CAAC;IAC3B;IACA,IAAIqB,GAAG,GAAG,EAAE;IACZ,IAAIuB,IAAI,GAAGd,QAAQ,CAAC,CAAC;IACrB,IAAIvB,KAAK,EAAEsC,KAAK,CAAC,CAAC;IAClB,IAAIC,IAAI,GAAGlB,IAAI,CAACD,GAAG,CAACgB,IAAI,CAAC7B,KAAK,EAAE6B,IAAI,CAAC5B,MAAM,CAAC,CAAC,CAAC;IAC9C,IAAIgC,aAAa,GAAG/B,iBAAiB,CAACb,QAAQ,EAAEwC,IAAI,CAAC7B,KAAK,GAAG6B,IAAI,CAAC5B,MAAM,GAAGf,IAAI,CAACJ,cAAc,CAAC,CAAC;IAChG,IAAIoD,YAAY,GAAGD,aAAa,CAACE,KAAK,CAAC,CAAC;IACxC5B,GAAG,CAACF,IAAI,GAAG,CAAC;IACZ,OAAO6B,YAAY,CAAC7I,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA;MACAkH,GAAG,CAAC1D,IAAI,CAAC4C,KAAK,GAAGyC,YAAY,CAAC,CAAC,CAAC,CAAC;MACjC3B,GAAG,CAACF,IAAI,IAAIZ,KAAK,CAACY,IAAI;MACtB0B,KAAK,GAAGzB,aAAa,CAACC,GAAG,EAAEyB,IAAI,EAAEvB,WAAW,CAAC;MAC7C,IAAIsB,KAAK,IAAID,IAAI,EAAE;QACjB;QACAI,YAAY,CAACE,KAAK,CAAC,CAAC;QACpBN,IAAI,GAAGC,KAAK;MACd,CAAC,MAAM;QACL;QACAxB,GAAG,CAACF,IAAI,IAAIE,GAAG,CAAC8B,GAAG,CAAC,CAAC,CAAChC,IAAI;QAC1BwB,IAAI,GAAGF,QAAQ,CAACpB,GAAG,EAAEyB,IAAI,EAAEH,IAAI,EAAE,KAAK,CAAC;QACvCG,IAAI,GAAGlB,IAAI,CAACD,GAAG,CAACgB,IAAI,CAAC7B,KAAK,EAAE6B,IAAI,CAAC5B,MAAM,CAAC;QACxCM,GAAG,CAAClH,MAAM,GAAGkH,GAAG,CAACF,IAAI,GAAG,CAAC;QACzByB,IAAI,GAAGd,QAAQ;MACjB;IACF;IACA,IAAIT,GAAG,CAAClH,MAAM,EAAE;MACdwI,IAAI,GAAGF,QAAQ,CAACpB,GAAG,EAAEyB,IAAI,EAAEH,IAAI,EAAE,IAAI,CAAC;MACtCtB,GAAG,CAAClH,MAAM,GAAGkH,GAAG,CAACF,IAAI,GAAG,CAAC;IAC3B;IACA,OAAOvD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDG,QAAQ,EAAE4C,aAAa,CAACzC,GAAG,CAAC,UAAU8C,CAAC,EAAE;QACvC,OAAOV,QAAQ,CAACU,CAAC,EAAE7B,WAAW,CAAC;MACjC,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOvB,IAAI;AACb,CAAC;AACD,IAAIqD,YAAY,GAAG;EACjBC,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE,KAAK;EAC1BC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC1D,SAASD,OAAOA,CAAA,EAAG;IACjB,IAAIE,KAAK;IACT9I,eAAe,CAAC,IAAI,EAAE4I,OAAO,CAAC;IAC9B,KAAK,IAAIG,IAAI,GAAG7J,SAAS,CAACC,MAAM,EAAE6J,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGhK,SAAS,CAACgK,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAG/H,UAAU,CAAC,IAAI,EAAE6H,OAAO,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IAClDlG,eAAe,CAACgG,KAAK,EAAE,OAAO,EAAElG,aAAa,CAAC,CAAC,CAAC,EAAEyF,YAAY,CAAC,CAAC;IAChEvF,eAAe,CAACgG,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIM,cAAc,GAAGN,KAAK,CAACzI,KAAK,CAAC+I,cAAc;MAC/CN,KAAK,CAACO,QAAQ,CAAC;QACbd,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIhF,UAAU,CAAC6F,cAAc,CAAC,EAAE;QAC9BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFtG,eAAe,CAACgG,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAIQ,gBAAgB,GAAGR,KAAK,CAACzI,KAAK,CAACiJ,gBAAgB;MACnDR,KAAK,CAACO,QAAQ,CAAC;QACbd,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIhF,UAAU,CAAC+F,gBAAgB,CAAC,EAAE;QAChCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOR,KAAK;EACd;EACA/G,SAAS,CAAC6G,OAAO,EAAEC,cAAc,CAAC;EAClC,OAAOjI,YAAY,CAACgI,OAAO,EAAE,CAAC;IAC5BvJ,GAAG,EAAE,kBAAkB;IACvB8C,KAAK,EAAE,SAASoH,gBAAgBA,CAACvE,IAAI,EAAE/D,CAAC,EAAE;MACxCA,CAAC,CAACuI,OAAO,CAAC,CAAC;MACX,IAAIC,WAAW,GAAG,IAAI,CAACpJ,KAAK;QAC1BqJ,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCvE,QAAQ,GAAGsE,WAAW,CAACtE,QAAQ;MACjC,IAAIwE,WAAW,GAAGlF,eAAe,CAACU,QAAQ,EAAErB,OAAO,CAAC;MACpD,IAAI6F,WAAW,EAAE;QACf,IAAI,CAACN,QAAQ,CAAC;UACZf,eAAe,EAAE,IAAI;UACrBE,UAAU,EAAExD;QACd,CAAC,EAAE,YAAY;UACb,IAAI0E,YAAY,EAAE;YAChBA,YAAY,CAAC1E,IAAI,EAAE/D,CAAC,CAAC;UACvB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIyI,YAAY,EAAE;QACvBA,YAAY,CAAC1E,IAAI,EAAE/D,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,kBAAkB;IACvB8C,KAAK,EAAE,SAASyH,gBAAgBA,CAAC5E,IAAI,EAAE/D,CAAC,EAAE;MACxCA,CAAC,CAACuI,OAAO,CAAC,CAAC;MACX,IAAIK,YAAY,GAAG,IAAI,CAACxJ,KAAK;QAC3ByJ,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxC3E,QAAQ,GAAG0E,YAAY,CAAC1E,QAAQ;MAClC,IAAIwE,WAAW,GAAGlF,eAAe,CAACU,QAAQ,EAAErB,OAAO,CAAC;MACpD,IAAI6F,WAAW,EAAE;QACf,IAAI,CAACN,QAAQ,CAAC;UACZf,eAAe,EAAE,KAAK;UACtBE,UAAU,EAAE;QACd,CAAC,EAAE,YAAY;UACb,IAAIsB,YAAY,EAAE;YAChBA,YAAY,CAAC9E,IAAI,EAAE/D,CAAC,CAAC;UACvB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI6I,YAAY,EAAE;QACvBA,YAAY,CAAC9E,IAAI,EAAE/D,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACD5B,GAAG,EAAE,aAAa;IAClB8C,KAAK,EAAE,SAAS4H,WAAWA,CAAC/E,IAAI,EAAE;MAChC,IAAIgF,YAAY,GAAG,IAAI,CAAC3J,KAAK;QAC3B4J,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,IAAI,GAAGF,YAAY,CAACE,IAAI;MAC1B,IAAIA,IAAI,KAAK,MAAM,IAAIlF,IAAI,CAACG,QAAQ,EAAE;QACpC,IAAIgF,YAAY,GAAG,IAAI,CAAC9J,KAAK;UAC3ByF,KAAK,GAAGqE,YAAY,CAACrE,KAAK;UAC1BC,MAAM,GAAGoE,YAAY,CAACpE,MAAM;UAC5BqE,OAAO,GAAGD,YAAY,CAACC,OAAO;UAC9B7D,WAAW,GAAG4D,YAAY,CAAC5D,WAAW;QACxC,IAAI8D,IAAI,GAAGxF,WAAW,CAAC;UACrBE,KAAK,EAAE,CAAC;UACRC,IAAI,EAAEpC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/CY,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC,CAAC;UACFd,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAEkF;QACZ,CAAC,CAAC;QACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAE9D,WAAW,CAAC;QAC5C,IAAIoC,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;QACpCA,SAAS,CAAChG,IAAI,CAACqC,IAAI,CAAC;QACpB,IAAI,CAACqE,QAAQ,CAAC;UACZZ,UAAU,EAAEA,UAAU;UACtBC,WAAW,EAAE2B,IAAI;UACjB1B,SAAS,EAAEA;QACb,CAAC,CAAC;MACJ;MACA,IAAIsB,OAAO,EAAE;QACXA,OAAO,CAACjF,IAAI,CAAC;MACf;IACF;EACF,CAAC,EAAE;IACD3F,GAAG,EAAE,iBAAiB;IACtB8C,KAAK,EAAE,SAASoI,eAAeA,CAACvF,IAAI,EAAE/F,CAAC,EAAE;MACvC,IAAI0J,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;MACpC,IAAI6B,YAAY,GAAG,IAAI,CAACnK,KAAK;QAC3ByF,KAAK,GAAG0E,YAAY,CAAC1E,KAAK;QAC1BC,MAAM,GAAGyE,YAAY,CAACzE,MAAM;QAC5BqE,OAAO,GAAGI,YAAY,CAACJ,OAAO;QAC9B7D,WAAW,GAAGiE,YAAY,CAACjE,WAAW;MACxC,IAAI8D,IAAI,GAAGxF,WAAW,CAAC;QACrBE,KAAK,EAAE,CAAC;QACRC,IAAI,EAAEpC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/CY,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC,CAAC;QACFd,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAEkF;MACZ,CAAC,CAAC;MACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAE9D,WAAW,CAAC;MAC5CoC,SAAS,GAAGA,SAAS,CAACV,KAAK,CAAC,CAAC,EAAEhJ,CAAC,GAAG,CAAC,CAAC;MACrC,IAAI,CAACoK,QAAQ,CAAC;QACZZ,UAAU,EAAEA,UAAU;QACtBC,WAAW,EAAE1D,IAAI;QACjB2D,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDtJ,GAAG,EAAE,YAAY;IACjB8C,KAAK,EAAE,SAASsI,UAAUA,CAACC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAE;MACrD,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACzK,KAAK;QAC3B0K,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;QAClDC,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,uBAAuB,GAAGL,YAAY,CAACK,uBAAuB;QAC9DjB,IAAI,GAAGY,YAAY,CAACZ,IAAI;QACxBkB,WAAW,GAAGN,YAAY,CAACM,WAAW;QACtCC,UAAU,GAAGP,YAAY,CAACO,UAAU;MACtC,IAAI9C,mBAAmB,GAAG,IAAI,CAAC+B,KAAK,CAAC/B,mBAAmB;MACxD,IAAIzC,KAAK,GAAG6E,SAAS,CAAC7E,KAAK;QACzBC,MAAM,GAAG4E,SAAS,CAAC5E,MAAM;QACzBH,CAAC,GAAG+E,SAAS,CAAC/E,CAAC;QACfC,CAAC,GAAG8E,SAAS,CAAC9E,CAAC;QACfd,KAAK,GAAG4F,SAAS,CAAC5F,KAAK;MACzB,IAAIuG,UAAU,GAAGC,QAAQ,CAAC,EAAE,CAACpC,MAAM,CAAC,CAACvC,IAAI,CAAC4E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI1F,KAAK,CAAC,EAAE,EAAE,CAAC;MACzE,IAAI2F,KAAK,GAAG,CAAC,CAAC;MACd,IAAIb,MAAM,IAAIV,IAAI,KAAK,MAAM,EAAE;QAC7BuB,KAAK,GAAG;UACN/B,YAAY,EAAE,IAAI,CAACH,gBAAgB,CAACxK,IAAI,CAAC,IAAI,EAAE4L,SAAS,CAAC;UACzDb,YAAY,EAAE,IAAI,CAACF,gBAAgB,CAAC7K,IAAI,CAAC,IAAI,EAAE4L,SAAS,CAAC;UACzDV,OAAO,EAAE,IAAI,CAACF,WAAW,CAAChL,IAAI,CAAC,IAAI,EAAE4L,SAAS;QAChD,CAAC;MACH;MACA,IAAI,CAACI,iBAAiB,EAAE;QACtB,OAAO,aAAapH,KAAK,CAAC+H,aAAa,CAAC3H,KAAK,EAAE0H,KAAK,EAAE,IAAI,CAAC/M,WAAW,CAACiN,iBAAiB,CAACjB,OAAO,EAAE9H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+H,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAChJI,iBAAiB,EAAE,KAAK;UACxBI,uBAAuB,EAAE,KAAK;UAC9BrF,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdH,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA;QACL,CAAC,CAAC,EAAEqE,IAAI,EAAEmB,UAAU,CAAC,CAAC;MACxB;MACA,OAAO,aAAa1H,KAAK,CAAC+H,aAAa,CAAC7H,MAAM,EAAE;QAC9C+H,KAAK,EAAEZ,cAAc;QACrBa,QAAQ,EAAEZ,iBAAiB;QAC3Ba,QAAQ,EAAEf,iBAAiB;QAC3BgB,MAAM,EAAEb,eAAe;QACvB7L,GAAG,EAAE,UAAU,CAAC8J,MAAM,CAACiC,WAAW,CAAC;QACnCY,IAAI,EAAE;UACJpG,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC;QACDkG,EAAE,EAAE;UACFrG,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC;QACDuD,gBAAgB,EAAE,IAAI,CAAC4C,oBAAoB;QAC3C9C,cAAc,EAAE,IAAI,CAAC+C;MACvB,CAAC,EAAE,UAAUC,KAAK,EAAE;QAClB,IAAIC,KAAK,GAAGD,KAAK,CAACxG,CAAC;UACjB0G,KAAK,GAAGF,KAAK,CAACvG,CAAC;UACf0G,SAAS,GAAGH,KAAK,CAACtG,KAAK;UACvB0G,UAAU,GAAGJ,KAAK,CAACrG,MAAM;QAC3B,OAAO,aAAapC,KAAK,CAAC+H,aAAa,CAAC7H,MAAM,EAAE;UAC9CmI,IAAI,EAAE,YAAY,CAAC7C,MAAM,CAACmC,UAAU,EAAE,MAAM,CAAC,CAACnC,MAAM,CAACmC,UAAU,EAAE,KAAK,CAAC;UACvEW,EAAE,EAAE,iBAAiB;UACrBQ,aAAa,EAAE,WAAW;UAC1Bb,KAAK,EAAEZ,cAAc;UACrBe,MAAM,EAAEb,eAAe;UACvBY,QAAQ,EAAEf,iBAAiB;UAC3Bc,QAAQ,EAAEZ;QACZ,CAAC,EAAE,aAAatH,KAAK,CAAC+H,aAAa,CAAC3H,KAAK,EAAE0H,KAAK,EAAE,YAAY;UAC5D;UACA,IAAI1G,KAAK,GAAG,CAAC,IAAI,CAACwD,mBAAmB,EAAE;YACrC,OAAO,IAAI;UACb;UACA,OAAOsC,MAAM,CAACnM,WAAW,CAACiN,iBAAiB,CAACjB,OAAO,EAAE9H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+H,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;YACnGI,iBAAiB,EAAEA,iBAAiB;YACpCI,uBAAuB,EAAE,CAACA,uBAAuB;YACjDrF,KAAK,EAAEyG,SAAS;YAChBxG,MAAM,EAAEyG,UAAU;YAClB5G,CAAC,EAAEyG,KAAK;YACRxG,CAAC,EAAEyG;UACL,CAAC,CAAC,EAAEpC,IAAI,EAAEmB,UAAU,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhM,GAAG,EAAE,YAAY;IACjB8C,KAAK,EAAE,SAASuK,UAAUA,CAACrC,IAAI,EAAErF,IAAI,EAAE;MACrC,IAAI2H,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACvM,KAAK;QAC3BqK,OAAO,GAAGkC,YAAY,CAAClC,OAAO;QAC9BR,IAAI,GAAG0C,YAAY,CAAC1C,IAAI;MAC1B,IAAIS,SAAS,GAAG/H,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,WAAW,CAAC,IAAI,CAACtE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE2E,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACxGqF,IAAI,EAAEA;MACR,CAAC,CAAC;MACF,IAAIO,MAAM,GAAG,CAAC5F,IAAI,CAACG,QAAQ,IAAI,CAACH,IAAI,CAACG,QAAQ,CAAChG,MAAM;MACpD,IAAIuJ,WAAW,GAAG,IAAI,CAAC4B,KAAK,CAAC5B,WAAW;MACxC,IAAImE,kBAAkB,GAAG,CAACnE,WAAW,CAACvD,QAAQ,IAAI,EAAE,EAAE1C,MAAM,CAAC,UAAUqK,IAAI,EAAE;QAC3E,OAAOA,IAAI,CAAC/H,KAAK,KAAKC,IAAI,CAACD,KAAK,IAAI+H,IAAI,CAACC,IAAI,KAAK/H,IAAI,CAAC+H,IAAI;MAC7D,CAAC,CAAC;MACF,IAAI,CAACF,kBAAkB,CAAC1N,MAAM,IAAIkL,IAAI,CAACtF,KAAK,IAAImF,IAAI,KAAK,MAAM,EAAE;QAC/D,OAAO,IAAI;MACb;MACA,OAAO,aAAavG,KAAK,CAAC+H,aAAa,CAAC3H,KAAK,EAAE;QAC7C1E,GAAG,EAAE,wBAAwB,CAAC8J,MAAM,CAACwB,SAAS,CAAC/E,CAAC,EAAE,GAAG,CAAC,CAACuD,MAAM,CAACwB,SAAS,CAAC9E,CAAC,EAAE,GAAG,CAAC,CAACsD,MAAM,CAACwB,SAAS,CAACoC,IAAI,CAAC;QACtGC,SAAS,EAAE,yBAAyB,CAAC7D,MAAM,CAACnE,IAAI,CAACD,KAAK;MACxD,CAAC,EAAE,IAAI,CAAC0F,UAAU,CAACC,OAAO,EAAEC,SAAS,EAAEC,MAAM,CAAC,EAAE5F,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACG,QAAQ,CAAChG,MAAM,GAAG6F,IAAI,CAACG,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;QACzH,OAAOoH,MAAM,CAACD,UAAU,CAAC1H,IAAI,EAAEO,KAAK,CAAC;MACvC,CAAC,CAAC,GAAG,IAAI,CAAC;IACZ;EACF,CAAC,EAAE;IACDlG,GAAG,EAAE,gBAAgB;IACrB8C,KAAK,EAAE,SAAS8K,cAAcA,CAAA,EAAG;MAC/B,IAAIxE,UAAU,GAAG,IAAI,CAAC6B,KAAK,CAAC7B,UAAU;MACtC,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,IAAI;MACb;MACA,OAAO,IAAI,CAACiE,UAAU,CAACjE,UAAU,EAAEA,UAAU,CAAC;IAChD;EACF,CAAC,EAAE;IACDpJ,GAAG,EAAE,eAAe;IACpB8C,KAAK,EAAE,SAAS+K,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAAC9M,KAAK;QAC3B8E,QAAQ,GAAGgI,YAAY,CAAChI,QAAQ;QAChCiI,OAAO,GAAGD,YAAY,CAACC,OAAO;MAChC,IAAIzD,WAAW,GAAGlF,eAAe,CAACU,QAAQ,EAAErB,OAAO,CAAC;MACpD,IAAI,CAAC6F,WAAW,EAAE;QAChB,OAAO,IAAI;MACb;MACA,IAAI0D,YAAY,GAAG,IAAI,CAAChN,KAAK;QAC3ByF,KAAK,GAAGuH,YAAY,CAACvH,KAAK;QAC1BC,MAAM,GAAGsH,YAAY,CAACtH,MAAM;MAC9B,IAAIuH,WAAW,GAAG,IAAI,CAAChD,KAAK;QAC1BhC,eAAe,GAAGgF,WAAW,CAAChF,eAAe;QAC7CE,UAAU,GAAG8E,WAAW,CAAC9E,UAAU;MACrC,IAAI+E,OAAO,GAAG;QACZ3H,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACD,IAAIyH,UAAU,GAAGhF,UAAU,GAAG;QAC5B5C,CAAC,EAAE4C,UAAU,CAAC5C,CAAC,GAAG4C,UAAU,CAAC1C,KAAK,GAAG,CAAC;QACtCD,CAAC,EAAE2C,UAAU,CAAC3C,CAAC,GAAG2C,UAAU,CAACzC,MAAM,GAAG;MACxC,CAAC,GAAG,IAAI;MACR,IAAI0H,OAAO,GAAGnF,eAAe,IAAIE,UAAU,GAAG,CAAC;QAC7CiF,OAAO,EAAEjF,UAAU;QACnBuE,IAAI,EAAE5I,iBAAiB,CAACqE,UAAU,EAAE4E,OAAO,EAAE,EAAE,CAAC;QAChDjL,KAAK,EAAEgC,iBAAiB,CAACqE,UAAU,EAAE5D,cAAc;MACrD,CAAC,CAAC,GAAG,EAAE;MACP,OAAO,aAAajB,KAAK,CAAC+J,YAAY,CAAC/D,WAAW,EAAE;QAClD4D,OAAO,EAAEA,OAAO;QAChBI,MAAM,EAAErF,eAAe;QACvBkF,UAAU,EAAEA,UAAU;QACtBI,KAAK,EAAE,EAAE;QACTH,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;;IAEA;EACF,CAAC,EAAE;IACDpO,GAAG,EAAE,iBAAiB;IACtB8C,KAAK,EAAE,SAAS0L,eAAeA,CAAA,EAAG;MAChC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,aAAa,GAAG,IAAI,CAAC1N,KAAK;QAC5B+M,OAAO,GAAGW,aAAa,CAACX,OAAO;QAC/BY,gBAAgB,GAAGD,aAAa,CAACC,gBAAgB;MACnD,IAAIrF,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;MACpC,OAAO,aAAahF,KAAK,CAAC+H,aAAa,CAAC,KAAK,EAAE;QAC7CsB,SAAS,EAAE,qCAAqC;QAChDiB,KAAK,EAAE;UACLC,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE;QACb;MACF,CAAC,EAAExF,SAAS,CAACrD,GAAG,CAAC,UAAUwH,IAAI,EAAE7N,CAAC,EAAE;QAClC;QACA,IAAI8N,IAAI,GAAGtJ,GAAG,CAACqJ,IAAI,EAAEM,OAAO,EAAE,MAAM,CAAC;QACrC,IAAI1C,OAAO,GAAG,IAAI;QAClB,IAAK,aAAa/G,KAAK,CAACyK,cAAc,CAACJ,gBAAgB,CAAC,EAAE;UACxDtD,OAAO,GAAG,aAAa/G,KAAK,CAAC+J,YAAY,CAACM,gBAAgB,EAAElB,IAAI,EAAE7N,CAAC,CAAC;QACtE;QACA,IAAIsE,UAAU,CAACyK,gBAAgB,CAAC,EAAE;UAChCtD,OAAO,GAAGsD,gBAAgB,CAAClB,IAAI,EAAE7N,CAAC,CAAC;QACrC,CAAC,MAAM;UACLyL,OAAO,GAAGqC,IAAI;QAChB;QACA,QACE;UACA;UACApJ,KAAK,CAAC+H,aAAa,CAAC,KAAK,EAAE;YACzBzB,OAAO,EAAE6D,MAAM,CAACvD,eAAe,CAACxL,IAAI,CAAC+O,MAAM,EAAEhB,IAAI,EAAE7N,CAAC,CAAC;YACrDI,GAAG,EAAE,aAAa,CAAC8J,MAAM,CAAC9E,QAAQ,CAAC,CAAC,CAAC;YACrC2I,SAAS,EAAE,iCAAiC;YAC5CiB,KAAK,EAAE;cACLI,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAE,cAAc;cACvBC,OAAO,EAAE,OAAO;cAChBC,UAAU,EAAE,MAAM;cAClBC,KAAK,EAAE,MAAM;cACbC,WAAW,EAAE;YACf;UACF,CAAC,EAAEhE,OAAO;QAAC;MAEf,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDrL,GAAG,EAAE,QAAQ;IACb8C,KAAK,EAAE,SAASwM,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACjK,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAIkK,aAAa,GAAG,IAAI,CAACvO,KAAK;QAC5ByF,KAAK,GAAG8I,aAAa,CAAC9I,KAAK;QAC3BC,MAAM,GAAG6I,aAAa,CAAC7I,MAAM;QAC7BiH,SAAS,GAAG4B,aAAa,CAAC5B,SAAS;QACnCiB,KAAK,GAAGW,aAAa,CAACX,KAAK;QAC3B9I,QAAQ,GAAGyJ,aAAa,CAACzJ,QAAQ;QACjC+E,IAAI,GAAG0E,aAAa,CAAC1E,IAAI;QACzB2E,MAAM,GAAGpP,wBAAwB,CAACmP,aAAa,EAAEvQ,SAAS,CAAC;MAC7D,IAAIyQ,KAAK,GAAGnK,WAAW,CAACkK,MAAM,EAAE,KAAK,CAAC;MACtC,OAAO,aAAalL,KAAK,CAAC+H,aAAa,CAAC,KAAK,EAAE;QAC7CsB,SAAS,EAAEtJ,IAAI,CAAC,kBAAkB,EAAEsJ,SAAS,CAAC;QAC9CiB,KAAK,EAAErL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqL,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDxG,QAAQ,EAAE,UAAU;UACpB4G,MAAM,EAAE,SAAS;UACjBvI,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC,CAAC;QACFgJ,IAAI,EAAE;MACR,CAAC,EAAE,aAAapL,KAAK,CAAC+H,aAAa,CAAC1H,OAAO,EAAEpF,QAAQ,CAAC,CAAC,CAAC,EAAEkQ,KAAK,EAAE;QAC/DhJ,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEmE,IAAI,KAAK,MAAM,GAAGnE,MAAM,GAAG,EAAE,GAAGA;MAC1C,CAAC,CAAC,EAAE,IAAI,CAACkH,cAAc,CAAC,CAAC,EAAEzI,iBAAiB,CAACW,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC+H,aAAa,CAAC,CAAC,EAAEhD,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC2D,eAAe,CAAC,CAAC,CAAC;IAC3H;EACF,CAAC,CAAC,EAAE,CAAC;IACHxO,GAAG,EAAE,0BAA0B;IAC/B8C,KAAK,EAAE,SAAS6M,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAACE,IAAI,KAAKD,SAAS,CAACE,QAAQ,IAAIH,SAAS,CAAC/E,IAAI,KAAKgF,SAAS,CAACG,QAAQ,IAAIJ,SAAS,CAACnJ,KAAK,KAAKoJ,SAAS,CAACI,SAAS,IAAIL,SAAS,CAAClJ,MAAM,KAAKmJ,SAAS,CAACK,UAAU,IAAIN,SAAS,CAAC7E,OAAO,KAAK8E,SAAS,CAACM,WAAW,IAAIP,SAAS,CAAC1I,WAAW,KAAK2I,SAAS,CAACO,eAAe,EAAE;QAChR,IAAIpF,IAAI,GAAGxF,WAAW,CAAC;UACrBE,KAAK,EAAE,CAAC;UACRC,IAAI,EAAE;YACJG,QAAQ,EAAE8J,SAAS,CAACE,IAAI;YACxBvJ,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAEmJ,SAAS,CAACnJ,KAAK;YACtBC,MAAM,EAAEkJ,SAAS,CAAClJ;UACpB,CAAC;UACDd,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAE+J,SAAS,CAAC7E;QACtB,CAAC,CAAC;QACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAE4E,SAAS,CAAC1I,WAAW,CAAC;QACtD,OAAO3D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsM,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACrDzG,UAAU,EAAEA,UAAU;UACtBC,WAAW,EAAE2B,IAAI;UACjB1B,SAAS,EAAE,CAAC0B,IAAI,CAAC;UACjBoF,eAAe,EAAER,SAAS,CAAC1I,WAAW;UACtC6I,QAAQ,EAAEH,SAAS,CAACE,IAAI;UACxBG,SAAS,EAAEL,SAAS,CAACnJ,KAAK;UAC1ByJ,UAAU,EAAEN,SAAS,CAAClJ,MAAM;UAC5ByJ,WAAW,EAAEP,SAAS,CAAC7E,OAAO;UAC9BiF,QAAQ,EAAEJ,SAAS,CAAC/E;QACtB,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD7K,GAAG,EAAE,mBAAmB;IACxB8C,KAAK,EAAE,SAASwJ,iBAAiBA,CAACjB,OAAO,EAAEC,SAAS,EAAET,IAAI,EAAEmB,UAAU,EAAE;MACtE,IAAK,aAAa1H,KAAK,CAACyK,cAAc,CAAC1D,OAAO,CAAC,EAAE;QAC/C,OAAO,aAAa/G,KAAK,CAAC+J,YAAY,CAAChD,OAAO,EAAEC,SAAS,CAAC;MAC5D;MACA,IAAIpH,UAAU,CAACmH,OAAO,CAAC,EAAE;QACvB,OAAOA,OAAO,CAACC,SAAS,CAAC;MAC3B;MACA;MACA,IAAI/E,CAAC,GAAG+E,SAAS,CAAC/E,CAAC;QACjBC,CAAC,GAAG8E,SAAS,CAAC9E,CAAC;QACfC,KAAK,GAAG6E,SAAS,CAAC7E,KAAK;QACvBC,MAAM,GAAG4E,SAAS,CAAC5E,MAAM;QACzBd,KAAK,GAAG0F,SAAS,CAAC1F,KAAK;MACzB,IAAIyK,KAAK,GAAG,IAAI;MAChB,IAAI5J,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAI4E,SAAS,CAACxF,QAAQ,IAAI+E,IAAI,KAAK,MAAM,EAAE;QACtEwF,KAAK,GAAG,aAAa/L,KAAK,CAAC+H,aAAa,CAACzH,OAAO,EAAE;UAChD0L,MAAM,EAAE,CAAC;YACP/J,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG;UAClB,CAAC,EAAE;YACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;UACtB,CAAC,EAAE;YACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;UACtB,CAAC;QACH,CAAC,CAAC;MACJ;MACA,IAAI6J,IAAI,GAAG,IAAI;MACf,IAAIC,QAAQ,GAAGvL,aAAa,CAACqG,SAAS,CAACoC,IAAI,CAAC;MAC5C,IAAIjH,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAI8J,QAAQ,CAAC/J,KAAK,GAAGA,KAAK,IAAI+J,QAAQ,CAAC9J,MAAM,GAAGA,MAAM,EAAE;QACnF6J,IAAI,GAAG,aAAajM,KAAK,CAAC+H,aAAa,CAAC,MAAM,EAAE;UAC9C9F,CAAC,EAAEA,CAAC,GAAG,CAAC;UACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;UACrB+J,QAAQ,EAAE;QACZ,CAAC,EAAEnF,SAAS,CAACoC,IAAI,CAAC;MACpB;MACA,IAAIgD,MAAM,GAAG1E,UAAU,IAAIjH,WAAW;MACtC,OAAO,aAAaT,KAAK,CAAC+H,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa/H,KAAK,CAAC+H,aAAa,CAACxH,SAAS,EAAEtF,QAAQ,CAAC;QACtGoR,IAAI,EAAErF,SAAS,CAAC5F,KAAK,GAAG,CAAC,GAAGgL,MAAM,CAAC9K,KAAK,GAAG8K,MAAM,CAAC5Q,MAAM,CAAC,GAAG,qBAAqB;QACjF8Q,MAAM,EAAE;MACV,CAAC,EAAEzM,IAAI,CAACmH,SAAS,EAAE,UAAU,CAAC,EAAE;QAC9BoE,IAAI,EAAE;MACR,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAEE,IAAI,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAChM,aAAa,CAAC;AAChBd,eAAe,CAAC8F,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClD9F,eAAe,CAAC8F,OAAO,EAAE,cAAc,EAAE;EACvCrC,WAAW,EAAE,GAAG,IAAI,CAAC,GAAGK,IAAI,CAACsJ,IAAI,CAAC,CAAC,CAAC,CAAC;EACrC9F,OAAO,EAAE,OAAO;EAChBF,IAAI,EAAE,MAAM;EACZa,iBAAiB,EAAE,CAACxG,MAAM,CAAC4L,KAAK;EAChChF,uBAAuB,EAAE,CAAC5G,MAAM,CAAC4L,KAAK;EACtCnF,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}