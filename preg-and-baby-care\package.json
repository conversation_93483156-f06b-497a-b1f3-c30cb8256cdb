{"name": "preg-and-baby-care", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google/generative-ai": "^0.24.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "better-sqlite3": "^11.10.0", "body-parser": "^2.2.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "sqlite3": "^5.1.7", "stream-browserify": "^3.0.0", "tailwind-merge": "^3.3.0", "url": "^0.11.4", "util": "^0.12.5", "uuid": "^11.1.0", "vm-browserify": "^1.1.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"process": "^0.11.10", "react-app-rewired": "^2.2.1", "webpack": "^5.99.8"}}