{"ast": null, "code": "module.exports = require('./head');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/lodash/first.js"], "sourcesContent": ["module.exports = require('./head');\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}