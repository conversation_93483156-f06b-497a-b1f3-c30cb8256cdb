{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachYearOfInterval} function options.\n */\n\n/**\n * The {@link eachYearOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\nexport function eachYearOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setMonth(0, 1);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setFullYear(date.getFullYear() + step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachYearOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "constructFrom", "eachYearOfInterval", "interval", "options", "start", "end", "in", "reversed", "endTime", "date", "setHours", "setMonth", "step", "dates", "push", "setFullYear", "getFullYear", "reverse"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/date-fns/eachYearOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachYearOfInterval} function options.\n */\n\n/**\n * The {@link eachYearOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\nexport function eachYearOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setMonth(0, 1);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setFullYear(date.getFullYear() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachYearOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACpD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGN,iBAAiB,CAACI,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAE/D,IAAIK,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EAC5B,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EACxC,MAAMI,IAAI,GAAGF,QAAQ,GAAGF,GAAG,GAAGD,KAAK;EACnCK,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzBD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAEnB,IAAIC,IAAI,GAAGT,OAAO,EAAES,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZL,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMM,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACJ,IAAI,IAAID,OAAO,EAAE;IACvBK,KAAK,CAACC,IAAI,CAACd,aAAa,CAACI,KAAK,EAAEK,IAAI,CAAC,CAAC;IACtCA,IAAI,CAACM,WAAW,CAACN,IAAI,CAACO,WAAW,CAAC,CAAC,GAAGJ,IAAI,CAAC;EAC7C;EAEA,OAAOL,QAAQ,GAAGM,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;AAC3C;;AAEA;AACA,eAAeZ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}