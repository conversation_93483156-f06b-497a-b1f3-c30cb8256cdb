{"ast": null, "code": "/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n  HOT_SPAN = 16;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeNow = Date.now;\n\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\nfunction shortOut(func) {\n  var count = 0,\n    lastCalled = 0;\n  return function () {\n    var stamp = nativeNow(),\n      remaining = HOT_SPAN - (stamp - lastCalled);\n    lastCalled = stamp;\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n    return func.apply(undefined, arguments);\n  };\n}\nmodule.exports = shortOut;", "map": {"version": 3, "names": ["HOT_COUNT", "HOT_SPAN", "nativeNow", "Date", "now", "shortOut", "func", "count", "lastCalled", "stamp", "remaining", "arguments", "apply", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/lodash/_shortOut.js"], "sourcesContent": ["/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n    HOT_SPAN = 16;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeNow = Date.now;\n\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\nfunction shortOut(func) {\n  var count = 0,\n      lastCalled = 0;\n\n  return function() {\n    var stamp = nativeNow(),\n        remaining = HOT_SPAN - (stamp - lastCalled);\n\n    lastCalled = stamp;\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n    return func.apply(undefined, arguments);\n  };\n}\n\nmodule.exports = shortOut;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAG,GAAG;EACfC,QAAQ,GAAG,EAAE;;AAEjB;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,KAAK,GAAG,CAAC;IACTC,UAAU,GAAG,CAAC;EAElB,OAAO,YAAW;IAChB,IAAIC,KAAK,GAAGP,SAAS,CAAC,CAAC;MACnBQ,SAAS,GAAGT,QAAQ,IAAIQ,KAAK,GAAGD,UAAU,CAAC;IAE/CA,UAAU,GAAGC,KAAK;IAClB,IAAIC,SAAS,GAAG,CAAC,EAAE;MACjB,IAAI,EAAEH,KAAK,IAAIP,SAAS,EAAE;QACxB,OAAOW,SAAS,CAAC,CAAC,CAAC;MACrB;IACF,CAAC,MAAM;MACLJ,KAAK,GAAG,CAAC;IACX;IACA,OAAOD,IAAI,CAACM,KAAK,CAACC,SAAS,EAAEF,SAAS,CAAC;EACzC,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}