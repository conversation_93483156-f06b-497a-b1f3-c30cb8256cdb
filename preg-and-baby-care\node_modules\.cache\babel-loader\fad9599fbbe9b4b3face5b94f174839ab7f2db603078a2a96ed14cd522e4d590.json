{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"9lu3g6\"\n}], [\"path\", {\n  d: \"M12 12h.01\",\n  key: \"1mp3jc\"\n}], [\"path\", {\n  d: \"M17 12h.01\",\n  key: \"1m0b6t\"\n}], [\"path\", {\n  d: \"M7 12h.01\",\n  key: \"eqddd0\"\n}]];\nconst RectangleEllipsis = createLucideIcon(\"rectangle-ellipsis\", __iconNode);\nexport { __iconNode, RectangleEllipsis as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "RectangleEllipsis", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\node_modules\\lucide-react\\src\\icons\\rectangle-ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '12', x: '2', y: '6', rx: '2', key: '9lu3g6' }],\n  ['path', { d: 'M12 12h.01', key: '1mp3jc' }],\n  ['path', { d: 'M17 12h.01', key: '1m0b6t' }],\n  ['path', { d: 'M7 12h.01', key: 'eqddd0' }],\n];\n\n/**\n * @component @name RectangleEllipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjYiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTE3IDEyaC4wMSIgLz4KICA8cGF0aCBkPSJNNyAxMmguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rectangle-ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RectangleEllipsis = createLucideIcon('rectangle-ellipsis', __iconNode);\n\nexport default RectangleEllipsis;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaM,MAAAE,iBAAA,GAAoBC,gBAAiB,uBAAsBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}