// Import database service
import dbService from "./dbService";

// Authentication services
const authService = {
  login: async (usernameOrEmail, password) => {
    try {
      // Get users from the database
      const users = await dbService.getAll("users");

      // Find user by username or email
      const user = users.find(
        (u) => u.username === usernameOrEmail || u.email === usernameOrEmail
      );

      if (!user) {
        throw new Error("User not found");
      }

      // Check password (in a real app, this would involve hashing)
      if (user.password_hash !== password) {
        throw new Error("Invalid password");
      }

      // Generate a mock token
      const token = `mock-token-${user.id}-${Date.now()}`;

      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.role === "admin",
        },
        token,
      };
    } catch (error) {
      throw new Error(error.message || "Login failed");
    }
  },

  signup: async (username, password, email) => {
    try {
      // Get users from the database
      const users = await dbService.getAll("users");

      // Check if username or email already exists
      const existingUser = users.find(
        (u) => u.username === username || u.email === email
      );

      if (existingUser) {
        if (existingUser.username === username) {
          throw new Error("Username already taken");
        }
        if (existingUser.email === email) {
          throw new Error("Email already registered");
        }
      }

      // Create new user
      const newUser = await dbService.create("users", {
        username,
        email,
        password_hash: password, // In a real app, this would be hashed
        role: "user", // Regular users are not admins by default
      });

      // Generate a mock token
      const token = `mock-token-${newUser.id}-${Date.now()}`;

      return {
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          isAdmin: newUser.role === "admin",
        },
        token,
      };
    } catch (error) {
      throw new Error(error.message || "Signup failed");
    }
  },

  logout: async () => {
    try {
      // With JWT, we don't need to do anything server-side for logout
      // Just remove the token from localStorage
      localStorage.removeItem("token");
      return { success: true };
    } catch (error) {
      throw new Error(error.message || "Logout failed");
    }
  },

  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem("token");
      const storedUser = JSON.parse(localStorage.getItem("user") || "null");

      if (!token || !storedUser) {
        throw new Error("No token or user data provided");
      }

      // Validate the user exists in our database
      const users = await dbService.getAll("users");
      const user = users.find((u) => u.id === storedUser.id);

      if (!user) {
        throw new Error("User not found");
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        isAdmin: user.role === "admin",
      };
    } catch (error) {
      throw new Error(error.message || "Failed to get current user");
    }
  },

  // Helper function to check if a user is an admin
  isAdmin: async () => {
    try {
      const storedUser = JSON.parse(localStorage.getItem("user") || "null");

      if (!storedUser) {
        return false;
      }

      // Check if the user has admin privileges
      return storedUser.isAdmin === true;
    } catch (error) {
      console.error("Error checking admin status:", error);
      return false; // On error, return false for security
    }
  },

  // Helper function to get the auth token
  getToken: () => {
    return localStorage.getItem("token");
  },
};

export default authService;
