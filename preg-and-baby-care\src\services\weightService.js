import dbService from "./dbService";

// Define the WeightEntry type
export const WeightEntry = {
  id: "",
  user_id: "",
  date: "",
  weight: 0,
  week: 0,
};

// Get the current user's ID
const getUserId = () => {
  const user = JSON.parse(localStorage.getItem("user") || "null");
  console.log("Weight Service - Current user from localStorage:", user);
  return user?.id;
};

// Save a weight entry to Supabase
export const saveWeightEntry = async (entry) => {
  console.log("Weight Service - Attempting to save entry:", entry);
  const userId = getUserId();
  console.log("Weight Service - User ID:", userId);

  if (!userId) {
    console.error("Weight Service - No user ID found");
    throw new Error("User not authenticated");
  }

  const entryData = {
    user_id: userId,
    date: entry.date,
    weight: entry.weight,
    week: entry.week,
    type: entry.type || "pregnancy",
  };

  // Add optional fields if they exist
  if (entry.notes) entryData.notes = entry.notes;
  if (entry.baby_name) entryData.baby_name = entry.baby_name;
  if (entry.age_unit) entryData.age_unit = entry.age_unit;
  if (entry.original_age) entryData.original_age = entry.original_age;

  console.log("Weight Service - Entry data to save:", entryData);

  try {
    const data = await dbService.create("weight_entries", entryData);
    console.log("Weight Service - Successfully saved entry:", data);
    return data;
  } catch (error) {
    console.error("Weight Service - Database error:", error);
    throw new Error(`Failed to save weight entry: ${error.message}`);
  }
};

// Get all weight entries for the current user
export const getWeightEntries = async () => {
  const userId = getUserId();

  if (!userId) {
    return [];
  }

  try {
    const entries = await dbService.getAll("weight_entries", {
      user_id: userId,
    });

    // Sort by week (ascending)
    return entries.sort((a, b) => a.week - b.week);
  } catch (error) {
    console.error("Error fetching weight entries:", error);
    throw new Error("Failed to fetch weight entries");
  }
};

// Delete a weight entry
export const deleteWeightEntry = async (id) => {
  const userId = getUserId();

  if (!userId) {
    throw new Error("User not authenticated");
  }

  const { error } = await supabase
    .from("weight_entries")
    .delete()
    .eq("id", id)
    .eq("user_id", userId);

  if (error) {
    console.error("Error deleting weight entry:", error);
    throw new Error("Failed to delete weight entry");
  }
};

// Save pregnancy info
export const savePregnancyInfo = async (info) => {
  const userId = getUserId();

  if (!userId) {
    throw new Error("User not authenticated");
  }

  // Check if pregnancy info already exists for this user
  const { data: existingInfo } = await supabase
    .from("pregnancy_info")
    .select("*")
    .eq("user_id", userId)
    .maybeSingle();

  let result;

  if (existingInfo) {
    // Update existing record
    const { data, error } = await supabase
      .from("pregnancy_info")
      .update({
        pre_pregnancy_weight: info.pre_pregnancy_weight,
        height_feet: info.height_feet,
        height_inches: info.height_inches,
        bmi_category: info.bmi_category,
      })
      .eq("user_id", userId)
      .select()
      .single();

    if (error) {
      console.error("Error updating pregnancy info:", error);
      throw new Error("Failed to update pregnancy information");
    }

    result = data;
  } else {
    // Insert new record
    const { data, error } = await supabase
      .from("pregnancy_info")
      .insert([
        {
          user_id: userId,
          pre_pregnancy_weight: info.pre_pregnancy_weight,
          height_feet: info.height_feet,
          height_inches: info.height_inches,
          bmi_category: info.bmi_category,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error saving pregnancy info:", error);
      throw new Error("Failed to save pregnancy information");
    }

    result = data;
  }

  return result;
};

// Get pregnancy info for the current user
export const getPregnancyInfo = async () => {
  const userId = getUserId();

  if (!userId) {
    return null;
  }

  const { data, error } = await supabase
    .from("pregnancy_info")
    .select("*")
    .eq("user_id", userId)
    .maybeSingle();

  if (error) {
    console.error("Error fetching pregnancy info:", error);
    throw new Error("Failed to fetch pregnancy information");
  }

  return data;
};
