import dbService from "./dbService";

// Define the WeightEntry type
export const WeightEntry = {
  id: "",
  user_id: "",
  date: "",
  weight: 0,
  week: 0,
};

// Get the current user's ID
const getUserId = () => {
  const user = JSON.parse(localStorage.getItem("user") || "null");
  console.log("Weight Service - Current user from localStorage:", user);
  return user?.id;
};

// Save a weight entry to database
export const saveWeightEntry = async (entry) => {
  console.log("Weight Service - Attempting to save entry:", entry);
  const userId = getUserId();
  console.log("Weight Service - User ID:", userId);

  if (!userId) {
    console.error("Weight Service - No user ID found");
    throw new Error("User not authenticated");
  }

  const entryData = {
    user_id: userId,
    date: entry.date,
    weight: entry.weight,
    week: entry.week,
    type: entry.type || "pregnancy",
  };

  // Add optional fields if they exist
  if (entry.notes) entryData.notes = entry.notes;
  if (entry.baby_name) entryData.baby_name = entry.baby_name;
  if (entry.age_unit) entryData.age_unit = entry.age_unit;
  if (entry.original_age) entryData.original_age = entry.original_age;

  console.log("Weight Service - Entry data to save:", entryData);

  try {
    const data = await dbService.create("weight_entries", entryData);
    console.log("Weight Service - Successfully saved entry:", data);
    return data;
  } catch (error) {
    console.error("Weight Service - Database error:", error);
    throw new Error(`Failed to save weight entry: ${error.message}`);
  }
};

// Get all weight entries for the current user
export const getWeightEntries = async () => {
  const userId = getUserId();

  if (!userId) {
    return [];
  }

  try {
    const entries = await dbService.getAll("weight_entries", {
      user_id: userId,
    });

    // Sort by week (ascending)
    return entries.sort((a, b) => a.week - b.week);
  } catch (error) {
    console.error("Error fetching weight entries:", error);
    throw new Error("Failed to fetch weight entries");
  }
};

// Delete a weight entry
export const deleteWeightEntry = async (id) => {
  const userId = getUserId();

  if (!userId) {
    throw new Error("User not authenticated");
  }

  try {
    // First verify the entry belongs to the user
    const entry = await dbService.getById("weight_entries", id);
    if (!entry || entry.user_id !== userId) {
      throw new Error("Weight entry not found or access denied");
    }

    await dbService.delete("weight_entries", id);
  } catch (error) {
    console.error("Error deleting weight entry:", error);
    throw new Error("Failed to delete weight entry");
  }
};

// Save pregnancy info
export const savePregnancyInfo = async (info) => {
  const userId = getUserId();

  if (!userId) {
    throw new Error("User not authenticated");
  }

  try {
    // Check if pregnancy info already exists for this user
    const existingInfoArray = await dbService.getAll("pregnancy_info", {
      user_id: userId,
    });
    const existingInfo =
      existingInfoArray.length > 0 ? existingInfoArray[0] : null;

    let result;

    if (existingInfo) {
      // Update existing record
      result = await dbService.update("pregnancy_info", existingInfo.id, {
        pre_pregnancy_weight: info.pre_pregnancy_weight,
        height_feet: info.height_feet,
        height_inches: info.height_inches,
        bmi_category: info.bmi_category,
        updated_at: new Date().toISOString(),
      });
    } else {
      // Insert new record
      result = await dbService.create("pregnancy_info", {
        user_id: userId,
        pre_pregnancy_weight: info.pre_pregnancy_weight,
        height_feet: info.height_feet,
        height_inches: info.height_inches,
        bmi_category: info.bmi_category,
      });
    }

    return result;
  } catch (error) {
    console.error("Error saving pregnancy info:", error);
    throw new Error("Failed to save pregnancy information");
  }
};

// Get pregnancy info for the current user
export const getPregnancyInfo = async () => {
  const userId = getUserId();

  if (!userId) {
    return null;
  }

  try {
    const pregnancyInfoArray = await dbService.getAll("pregnancy_info", {
      user_id: userId,
    });
    return pregnancyInfoArray.length > 0 ? pregnancyInfoArray[0] : null;
  } catch (error) {
    console.error("Error fetching pregnancy info:", error);
    throw new Error("Failed to fetch pregnancy information");
  }
};
