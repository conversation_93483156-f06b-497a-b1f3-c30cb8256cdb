{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"b1zbii\"\n}], [\"path\", {\n  d: \"M22 20H2\",\n  key: \"1p1f7z\"\n}], [\"path\", {\n  d: \"M22 4H2\",\n  key: \"1b7qnq\"\n}]];\nconst AlignVerticalSpaceAround = createLucideIcon(\"align-vertical-space-around\", __iconNode);\nexport { __iconNode, AlignVerticalSpaceAround as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "AlignVerticalSpaceAround", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\node_modules\\lucide-react\\src\\icons\\align-vertical-space-around.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '10', height: '6', x: '7', y: '9', rx: '2', key: 'b1zbii' }],\n  ['path', { d: 'M22 20H2', key: '1p1f7z' }],\n  ['path', { d: 'M22 4H2', key: '1b7qnq' }],\n];\n\n/**\n * @component @name AlignVerticalSpaceAround\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iNiIgeD0iNyIgeT0iOSIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTIyIDIwSDIiIC8+CiAgPHBhdGggZD0iTTIyIDRIMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/align-vertical-space-around\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignVerticalSpaceAround = createLucideIcon('align-vertical-space-around', __iconNode);\n\nexport default AlignVerticalSpaceAround;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaM,MAAAE,wBAAA,GAA2BC,gBAAiB,gCAA+BT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}