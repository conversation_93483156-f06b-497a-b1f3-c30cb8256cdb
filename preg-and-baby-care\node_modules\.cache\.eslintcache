[{"D:\\Akka\\preg-and-baby-care\\src\\index.js": "1", "D:\\Akka\\preg-and-baby-care\\src\\App.js": "2", "D:\\Akka\\preg-and-baby-care\\src\\pages\\LoginPage.js": "3", "D:\\Akka\\preg-and-baby-care\\src\\pages\\HomePage.js": "4", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Schemes.js": "5", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyCare.js": "6", "D:\\Akka\\preg-and-baby-care\\src\\context\\AuthContext.js": "7", "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Navbar.js": "8", "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Footer.js": "9", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PageHeader.js": "10", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\Tabs.js": "11", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\TipCard.js": "12", "D:\\Akka\\preg-and-baby-care\\src\\services\\api.js": "13", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\InfoCard.js": "14", "D:\\Akka\\preg-and-baby-care\\src\\pages\\PregnancyCare.js": "15", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabySleep.js": "16", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSchemes.js": "17", "D:\\Akka\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js": "18", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Contact.js": "19", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PrivateRoute.js": "20", "D:\\Akka\\preg-and-baby-care\\src\\pages\\NotFound.js": "21", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js": "22", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Appointments.js": "23", "D:\\Akka\\preg-and-baby-care\\src\\pages\\WeightTracker.js": "24", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Nutrition.js": "25", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Exercise.js": "26", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyNutrition.js": "27", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyActivities.js": "28", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Vaccinations.js": "29", "D:\\Akka\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js": "30", "D:\\Akka\\preg-and-baby-care\\src\\pages\\TermsOfService.js": "31", "D:\\Akka\\preg-and-baby-care\\src\\pages\\CookiePolicy.js": "32", "D:\\Akka\\preg-and-baby-care\\src\\pages\\SignupPage.js": "33", "D:\\Akka\\preg-and-baby-care\\src\\services\\index.js": "34", "D:\\Akka\\preg-and-baby-care\\src\\services\\schemeService.js": "35", "D:\\Akka\\preg-and-baby-care\\src\\services\\authService.js": "36", "D:\\Akka\\preg-and-baby-care\\src\\services\\planService.js": "37", "D:\\Akka\\preg-and-baby-care\\src\\services\\nutritionService.js": "38", "D:\\Akka\\preg-and-baby-care\\src\\services\\exerciseService.js": "39", "D:\\Akka\\preg-and-baby-care\\src\\services\\weightTrackerService.js": "40", "D:\\Akka\\preg-and-baby-care\\src\\reportWebVitals.js": "41", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminNutrition.js": "42", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminExercise.js": "43", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js": "44", "D:\\Akka\\preg-and-baby-care\\src\\services\\dbService.js": "45", "D:\\Akka\\preg-and-baby-care\\src\\services\\supabaseClient.js": "46", "D:\\Akka\\preg-and-baby-care\\src\\config\\db.js": "47", "D:\\Akka\\preg-and-baby-care\\src\\services\\babyCareService.js": "48", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js": "49", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js": "50", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js": "51", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js": "52", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js": "53", "D:\\Akka\\preg-and-baby-care\\src\\components\\chatbot\\Chatbot.js": "54", "D:\\Akka\\preg-and-baby-care\\src\\services\\chatbotService.js": "55", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminChatbot.js": "56", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\index.js": "57", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\App.js": "58", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\reportWebVitals.js": "59", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\context\\AuthContext.js": "60", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\LoginPage.js": "61", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyCare.js": "62", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\HomePage.js": "63", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Contact.js": "64", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SignupPage.js": "65", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js": "66", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Schemes.js": "67", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Nutrition.js": "68", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabySleep.js": "69", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyCare.js": "70", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Exercise.js": "71", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Appointments.js": "72", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\TermsOfService.js": "73", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\CookiePolicy.js": "74", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\WeightTracker.js": "75", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js": "76", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyNutrition.js": "77", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminNutrition.js": "78", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\NotFound.js": "79", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Vaccinations.js": "80", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyActivities.js": "81", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminExercise.js": "82", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js": "83", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js": "84", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js": "85", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js": "86", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js": "87", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSchemes.js": "88", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js": "89", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js": "90", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Navbar.js": "91", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Footer.js": "92", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\PageHeader.js": "93", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Tabs.js": "94", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\InfoCard.js": "95", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\TipCard.js": "96", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\index.js": "97", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\planService.js": "98", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\schemeService.js": "99", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\nutritionService.js": "100", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightTrackerService.js": "101", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\exerciseService.js": "102", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\authService.js": "103", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\babyCareService.js": "104", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\dbService.js": "105", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\supabaseClient.js": "106", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightService.js": "107", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\hooks\\use-toast.js": "108", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\button.jsx": "109", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\label.jsx": "110", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\input.jsx": "111", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\card.jsx": "112", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\chart.jsx": "113", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\lib\\utils.js": "114", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\chatbotService.js": "115", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Chatbot.js": "116", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\appointmentService.js": "117", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SleepPatterns.js": "118", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\utils\\helpers.js": "119", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\geminiService.js": "120", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyFAQ.js": "121", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\MealPlans.js": "122", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\sqliteService.js": "123"}, {"size": 992, "mtime": 1746945169206, "results": "124", "hashOfConfig": "125"}, {"size": 4581, "mtime": 1747068972789, "results": "126", "hashOfConfig": "125"}, {"size": 2943, "mtime": 1747055493365, "results": "127", "hashOfConfig": "125"}, {"size": 7550, "mtime": 1746973547320, "results": "128", "hashOfConfig": "125"}, {"size": 4434, "mtime": 1746969318748, "results": "129", "hashOfConfig": "125"}, {"size": 17394, "mtime": 1747068922248, "results": "130", "hashOfConfig": "125"}, {"size": 2155, "mtime": 1746969292446, "results": "131", "hashOfConfig": "125"}, {"size": 3270, "mtime": 1746967763483, "results": "132", "hashOfConfig": "125"}, {"size": 3575, "mtime": 1746883645683, "results": "133", "hashOfConfig": "125"}, {"size": 496, "mtime": 1746880880576, "results": "134", "hashOfConfig": "125"}, {"size": 646, "mtime": 1746880909937, "results": "135", "hashOfConfig": "125"}, {"size": 407, "mtime": 1746880903944, "results": "136", "hashOfConfig": "125"}, {"size": 8154, "mtime": 1746940332820, "results": "137", "hashOfConfig": "125"}, {"size": 739, "mtime": 1746880898367, "results": "138", "hashOfConfig": "125"}, {"size": 10479, "mtime": 1746882930312, "results": "139", "hashOfConfig": "125"}, {"size": 6777, "mtime": 1746881897615, "results": "140", "hashOfConfig": "125"}, {"size": 11248, "mtime": 1746973519557, "results": "141", "hashOfConfig": "125"}, {"size": 12402, "mtime": 1746881949641, "results": "142", "hashOfConfig": "125"}, {"size": 8016, "mtime": 1746882001827, "results": "143", "hashOfConfig": "125"}, {"size": 560, "mtime": 1746966141891, "results": "144", "hashOfConfig": "125"}, {"size": 944, "mtime": 1746882159652, "results": "145", "hashOfConfig": "125"}, {"size": 264, "mtime": 1746882207797, "results": "146", "hashOfConfig": "125"}, {"size": 16882, "mtime": 1746884023296, "results": "147", "hashOfConfig": "125"}, {"size": 14253, "mtime": 1746969193187, "results": "148", "hashOfConfig": "125"}, {"size": 11243, "mtime": 1746973229592, "results": "149", "hashOfConfig": "125"}, {"size": 18677, "mtime": 1746973207296, "results": "150", "hashOfConfig": "125"}, {"size": 21927, "mtime": 1746884859498, "results": "151", "hashOfConfig": "125"}, {"size": 35356, "mtime": 1747069225381, "results": "152", "hashOfConfig": "125"}, {"size": 25442, "mtime": 1747069244168, "results": "153", "hashOfConfig": "125"}, {"size": 6636, "mtime": 1746889740424, "results": "154", "hashOfConfig": "125"}, {"size": 7926, "mtime": 1746889769945, "results": "155", "hashOfConfig": "125"}, {"size": 7514, "mtime": 1746889715640, "results": "156", "hashOfConfig": "125"}, {"size": 4520, "mtime": 1746966141070, "results": "157", "hashOfConfig": "125"}, {"size": 568, "mtime": 1747058595782, "results": "158", "hashOfConfig": "125"}, {"size": 3606, "mtime": 1747066381833, "results": "159", "hashOfConfig": "125"}, {"size": 3898, "mtime": 1747066203986, "results": "160", "hashOfConfig": "125"}, {"size": 2727, "mtime": 1747069297290, "results": "161", "hashOfConfig": "125"}, {"size": 4456, "mtime": 1747057786843, "results": "162", "hashOfConfig": "125"}, {"size": 8336, "mtime": 1747058279955, "results": "163", "hashOfConfig": "125"}, {"size": 3765, "mtime": 1747056969137, "results": "164", "hashOfConfig": "125"}, {"size": 362, "mtime": 1746967446669, "results": "165", "hashOfConfig": "125"}, {"size": 9380, "mtime": 1746968450856, "results": "166", "hashOfConfig": "125"}, {"size": 12288, "mtime": 1746968502689, "results": "167", "hashOfConfig": "125"}, {"size": 10579, "mtime": 1747057677261, "results": "168", "hashOfConfig": "125"}, {"size": 4539, "mtime": 1747057311281, "results": "169", "hashOfConfig": "125"}, {"size": 719, "mtime": 1747054728328, "results": "170", "hashOfConfig": "125"}, {"size": 1016, "mtime": 1747055159069, "results": "171", "hashOfConfig": "125"}, {"size": 12666, "mtime": 1747066740038, "results": "172", "hashOfConfig": "125"}, {"size": 12303, "mtime": 1747060294407, "results": "173", "hashOfConfig": "125"}, {"size": 11244, "mtime": 1747069176351, "results": "174", "hashOfConfig": "125"}, {"size": 13059, "mtime": 1747060427273, "results": "175", "hashOfConfig": "125"}, {"size": 5756, "mtime": 1747069010539, "results": "176", "hashOfConfig": "125"}, {"size": 11827, "mtime": 1747069205831, "results": "177", "hashOfConfig": "125"}, {"size": 5792, "mtime": 1747069161290, "results": "178", "hashOfConfig": "125"}, {"size": 12235, "mtime": 1747069115242, "results": "179", "hashOfConfig": "125"}, {"size": 8205, "mtime": 1747069320776, "results": "180", "hashOfConfig": "125"}, {"size": 992, "mtime": 1747834146064, "results": "181", "hashOfConfig": "182"}, {"size": 4820, "mtime": 1747981617964, "results": "183", "hashOfConfig": "182"}, {"size": 362, "mtime": 1747834146069, "results": "184", "hashOfConfig": "182"}, {"size": 2155, "mtime": 1747834146227, "results": "185", "hashOfConfig": "182"}, {"size": 2943, "mtime": 1747914817639, "results": "186", "hashOfConfig": "182"}, {"size": 11108, "mtime": 1747981669177, "results": "187", "hashOfConfig": "182"}, {"size": 7550, "mtime": 1747834146299, "results": "188", "hashOfConfig": "182"}, {"size": 8016, "mtime": 1747834146292, "results": "189", "hashOfConfig": "182"}, {"size": 4520, "mtime": 1747834146318, "results": "190", "hashOfConfig": "182"}, {"size": 13789, "mtime": 1752128925732, "results": "191", "hashOfConfig": "182"}, {"size": 6168, "mtime": 1747934604926, "results": "192", "hashOfConfig": "182"}, {"size": 8041, "mtime": 1747981571951, "results": "193", "hashOfConfig": "182"}, {"size": 6777, "mtime": 1747834146285, "results": "194", "hashOfConfig": "182"}, {"size": 17534, "mtime": 1747925828721, "results": "195", "hashOfConfig": "182"}, {"size": 7461, "mtime": 1747959747925, "results": "196", "hashOfConfig": "182"}, {"size": 35165, "mtime": 1747988557444, "results": "197", "hashOfConfig": "182"}, {"size": 9495, "mtime": 1747835445410, "results": "198", "hashOfConfig": "182"}, {"size": 9357, "mtime": 1747835522423, "results": "199", "hashOfConfig": "182"}, {"size": 62183, "mtime": 1747917882981, "results": "200", "hashOfConfig": "182"}, {"size": 7675, "mtime": 1747835477697, "results": "201", "hashOfConfig": "182"}, {"size": 9235, "mtime": 1747959375486, "results": "202", "hashOfConfig": "182"}, {"size": 9569, "mtime": 1747931113196, "results": "203", "hashOfConfig": "182"}, {"size": 944, "mtime": 1747834146304, "results": "204", "hashOfConfig": "182"}, {"size": 25344, "mtime": 1747834839709, "results": "205", "hashOfConfig": "182"}, {"size": 35258, "mtime": 1747834816845, "results": "206", "hashOfConfig": "182"}, {"size": 13841, "mtime": 1747959846467, "results": "207", "hashOfConfig": "182"}, {"size": 5674, "mtime": 1747834673594, "results": "208", "hashOfConfig": "182"}, {"size": 12359, "mtime": 1747931524761, "results": "209", "hashOfConfig": "182"}, {"size": 10579, "mtime": 1747834146274, "results": "210", "hashOfConfig": "182"}, {"size": 11415, "mtime": 1747926555793, "results": "211", "hashOfConfig": "182"}, {"size": 11126, "mtime": 1747959356535, "results": "212", "hashOfConfig": "182"}, {"size": 12000, "mtime": 1747934644949, "results": "213", "hashOfConfig": "182"}, {"size": 12689, "mtime": 1747932006745, "results": "214", "hashOfConfig": "182"}, {"size": 264, "mtime": 1747834146195, "results": "215", "hashOfConfig": "182"}, {"size": 3451, "mtime": 1752128808240, "results": "216", "hashOfConfig": "182"}, {"size": 3632, "mtime": 1747835547150, "results": "217", "hashOfConfig": "182"}, {"size": 496, "mtime": 1747834146189, "results": "218", "hashOfConfig": "182"}, {"size": 646, "mtime": 1747834146198, "results": "219", "hashOfConfig": "182"}, {"size": 739, "mtime": 1747834146179, "results": "220", "hashOfConfig": "182"}, {"size": 407, "mtime": 1747834146201, "results": "221", "hashOfConfig": "182"}, {"size": 701, "mtime": 1747920071398, "results": "222", "hashOfConfig": "182"}, {"size": 2727, "mtime": 1747834146356, "results": "223", "hashOfConfig": "182"}, {"size": 3606, "mtime": 1747834146359, "results": "224", "hashOfConfig": "182"}, {"size": 5867, "mtime": 1747963645570, "results": "225", "hashOfConfig": "182"}, {"size": 3765, "mtime": 1747834146363, "results": "226", "hashOfConfig": "182"}, {"size": 8414, "mtime": 1747935191418, "results": "227", "hashOfConfig": "182"}, {"size": 3939, "mtime": 1750169034317, "results": "228", "hashOfConfig": "182"}, {"size": 23019, "mtime": 1747960847452, "results": "229", "hashOfConfig": "182"}, {"size": 3134, "mtime": 1750168975435, "results": "230", "hashOfConfig": "182"}, {"size": 933, "mtime": 1750169059866, "results": "231", "hashOfConfig": "182"}, {"size": 4693, "mtime": 1747976789514, "results": "232", "hashOfConfig": "182"}, {"size": 686, "mtime": 1747887634716, "results": "233", "hashOfConfig": "182"}, {"size": 1647, "mtime": 1747888030880, "results": "234", "hashOfConfig": "182"}, {"size": 526, "mtime": 1747888108882, "results": "235", "hashOfConfig": "182"}, {"size": 690, "mtime": 1747888093926, "results": "236", "hashOfConfig": "182"}, {"size": 1586, "mtime": 1747888271618, "results": "237", "hashOfConfig": "182"}, {"size": 861, "mtime": 1747888192878, "results": "238", "hashOfConfig": "182"}, {"size": 136, "mtime": 1747887639145, "results": "239", "hashOfConfig": "182"}, {"size": 9546, "mtime": 1747983720310, "results": "240", "hashOfConfig": "182"}, {"size": 6368, "mtime": 1747983704393, "results": "241", "hashOfConfig": "182"}, {"size": 11081, "mtime": 1747920916361, "results": "242", "hashOfConfig": "182"}, {"size": 16327, "mtime": 1747930955715, "results": "243", "hashOfConfig": "182"}, {"size": 1994, "mtime": 1747834146371, "results": "244", "hashOfConfig": "182"}, {"size": 5788, "mtime": 1747962377378, "results": "245", "hashOfConfig": "182"}, {"size": 14308, "mtime": 1747983586878, "results": "246", "hashOfConfig": "182"}, {"size": 60097, "mtime": 1747987649577, "results": "247", "hashOfConfig": "182"}, {"size": 5805, "mtime": 1750169263458, "results": "248", "hashOfConfig": "182"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "100jhwu", {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1q0ycvk", {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Akka\\preg-and-baby-care\\src\\index.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\App.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\LoginPage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\HomePage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Schemes.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyCare.js", ["618"], [], "D:\\Akka\\preg-and-baby-care\\src\\context\\AuthContext.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Navbar.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Footer.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PageHeader.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\Tabs.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\TipCard.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\api.js", ["619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638"], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\InfoCard.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\PregnancyCare.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabySleep.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSchemes.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Contact.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PrivateRoute.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\NotFound.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Appointments.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\WeightTracker.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Nutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Exercise.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyNutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyActivities.js", ["639"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Vaccinations.js", ["640"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\TermsOfService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\CookiePolicy.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\SignupPage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\index.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\schemeService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\authService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\planService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\nutritionService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\exerciseService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\weightTrackerService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\reportWebVitals.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminNutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminExercise.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\dbService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\supabaseClient.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\config\\db.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\babyCareService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js", ["641"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js", ["642", "643", "644", "645", "646"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js", ["647"], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\chatbot\\Chatbot.js", ["648"], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\chatbotService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminChatbot.js", ["649"], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SignupPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Schemes.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Nutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabySleep.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Exercise.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Appointments.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\TermsOfService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\CookiePolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\WeightTracker.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Vaccinations.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyActivities.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminExercise.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSchemes.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\PageHeader.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Tabs.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\InfoCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\TipCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\planService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\schemeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\nutritionService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightTrackerService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\exerciseService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\babyCareService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\dbService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\supabaseClient.js", ["650"], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\hooks\\use-toast.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\card.jsx", [], ["651"], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\chart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\chatbotService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Chatbot.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\appointmentService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SleepPatterns.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\utils\\helpers.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\geminiService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyFAQ.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\MealPlans.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\sqliteService.js", [], [], {"ruleId": "652", "severity": 1, "message": "653", "line": 29, "column": 10, "nodeType": "654", "messageId": "655", "endLine": 29, "endColumn": 24}, {"ruleId": "656", "severity": 1, "message": "657", "line": 42, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 42, "endColumn": 58}, {"ruleId": "656", "severity": 1, "message": "657", "line": 62, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 62, "endColumn": 59}, {"ruleId": "656", "severity": 1, "message": "657", "line": 72, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 72, "endColumn": 59}, {"ruleId": "656", "severity": 1, "message": "657", "line": 82, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 82, "endColumn": 72}, {"ruleId": "656", "severity": 1, "message": "657", "line": 96, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 96, "endColumn": 69}, {"ruleId": "656", "severity": 1, "message": "657", "line": 111, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 111, "endColumn": 68}, {"ruleId": "656", "severity": 1, "message": "657", "line": 129, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 129, "endColumn": 66}, {"ruleId": "656", "severity": 1, "message": "657", "line": 140, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 140, "endColumn": 69}, {"ruleId": "656", "severity": 1, "message": "657", "line": 158, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 158, "endColumn": 66}, {"ruleId": "656", "severity": 1, "message": "657", "line": 170, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 170, "endColumn": 69}, {"ruleId": "656", "severity": 1, "message": "657", "line": 188, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 188, "endColumn": 66}, {"ruleId": "656", "severity": 1, "message": "657", "line": 196, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 196, "endColumn": 69}, {"ruleId": "656", "severity": 1, "message": "657", "line": 210, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 210, "endColumn": 67}, {"ruleId": "656", "severity": 1, "message": "657", "line": 225, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 225, "endColumn": 66}, {"ruleId": "656", "severity": 1, "message": "657", "line": 243, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 243, "endColumn": 64}, {"ruleId": "656", "severity": 1, "message": "657", "line": 254, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 254, "endColumn": 67}, {"ruleId": "656", "severity": 1, "message": "657", "line": 272, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 272, "endColumn": 64}, {"ruleId": "656", "severity": 1, "message": "657", "line": 284, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 284, "endColumn": 67}, {"ruleId": "656", "severity": 1, "message": "657", "line": 302, "column": 9, "nodeType": "658", "messageId": "659", "endLine": 302, "endColumn": 64}, {"ruleId": "656", "severity": 1, "message": "657", "line": 310, "column": 7, "nodeType": "658", "messageId": "659", "endLine": 310, "endColumn": 67}, {"ruleId": "660", "severity": 1, "message": "661", "line": 19, "column": 9, "nodeType": "662", "messageId": "663", "endLine": 21, "endColumn": 4}, {"ruleId": "660", "severity": 1, "message": "661", "line": 17, "column": 9, "nodeType": "662", "messageId": "663", "endLine": 19, "endColumn": 4}, {"ruleId": "660", "severity": 1, "message": "661", "line": 22, "column": 9, "nodeType": "662", "messageId": "663", "endLine": 24, "endColumn": 4}, {"ruleId": "652", "severity": 1, "message": "664", "line": 10, "column": 3, "nodeType": "654", "messageId": "655", "endLine": 10, "endColumn": 9}, {"ruleId": "652", "severity": 1, "message": "665", "line": 21, "column": 10, "nodeType": "654", "messageId": "655", "endLine": 21, "endColumn": 17}, {"ruleId": "652", "severity": 1, "message": "666", "line": 21, "column": 19, "nodeType": "654", "messageId": "655", "endLine": 21, "endColumn": 29}, {"ruleId": "652", "severity": 1, "message": "667", "line": 22, "column": 17, "nodeType": "654", "messageId": "655", "endLine": 22, "endColumn": 25}, {"ruleId": "652", "severity": 1, "message": "668", "line": 23, "column": 11, "nodeType": "654", "messageId": "655", "endLine": 23, "endColumn": 22}, {"ruleId": "660", "severity": 1, "message": "661", "line": 22, "column": 9, "nodeType": "662", "messageId": "663", "endLine": 24, "endColumn": 4}, {"ruleId": "669", "severity": 1, "message": "670", "line": 66, "column": 6, "nodeType": "671", "endLine": 66, "endColumn": 23, "suggestions": "672"}, {"ruleId": "660", "severity": 1, "message": "661", "line": 22, "column": 9, "nodeType": "662", "messageId": "663", "endLine": 24, "endColumn": 4}, {"ruleId": "673", "severity": 1, "message": "674", "line": 24, "column": 1, "nodeType": "675", "endLine": 24, "endColumn": 21}, {"ruleId": "676", "severity": 1, "message": "677", "line": 28, "column": 3, "nodeType": "678", "endLine": 35, "endColumn": 5, "suppressions": "679"}, "no-unused-vars", "'babyMilestones' is assigned a value but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "unexpected", "'faPlus' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'welcomeMessages'. Either include it or remove the dependency array.", "ArrayExpression", ["680"], "import/no-anonymous-default-export", "Assign literal to a variable before exporting as module default", "ExportDefaultDeclaration", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", ["681"], {"desc": "682", "fix": "683"}, {"kind": "684", "justification": "685"}, "Update the dependencies array to be: [isOpen, context, welcomeMessages]", {"range": "686", "text": "687"}, "directive", "", [1873, 1890], "[isOpen, context, welcomeMessages]"]