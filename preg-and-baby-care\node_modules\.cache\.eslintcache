[{"D:\\Akka\\preg-and-baby-care\\src\\index.js": "1", "D:\\Akka\\preg-and-baby-care\\src\\App.js": "2", "D:\\Akka\\preg-and-baby-care\\src\\pages\\LoginPage.js": "3", "D:\\Akka\\preg-and-baby-care\\src\\pages\\HomePage.js": "4", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Schemes.js": "5", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyCare.js": "6", "D:\\Akka\\preg-and-baby-care\\src\\context\\AuthContext.js": "7", "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Navbar.js": "8", "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Footer.js": "9", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PageHeader.js": "10", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\Tabs.js": "11", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\TipCard.js": "12", "D:\\Akka\\preg-and-baby-care\\src\\services\\api.js": "13", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\InfoCard.js": "14", "D:\\Akka\\preg-and-baby-care\\src\\pages\\PregnancyCare.js": "15", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabySleep.js": "16", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSchemes.js": "17", "D:\\Akka\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js": "18", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Contact.js": "19", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PrivateRoute.js": "20", "D:\\Akka\\preg-and-baby-care\\src\\pages\\NotFound.js": "21", "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js": "22", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Appointments.js": "23", "D:\\Akka\\preg-and-baby-care\\src\\pages\\WeightTracker.js": "24", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Nutrition.js": "25", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Exercise.js": "26", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyNutrition.js": "27", "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyActivities.js": "28", "D:\\Akka\\preg-and-baby-care\\src\\pages\\Vaccinations.js": "29", "D:\\Akka\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js": "30", "D:\\Akka\\preg-and-baby-care\\src\\pages\\TermsOfService.js": "31", "D:\\Akka\\preg-and-baby-care\\src\\pages\\CookiePolicy.js": "32", "D:\\Akka\\preg-and-baby-care\\src\\pages\\SignupPage.js": "33", "D:\\Akka\\preg-and-baby-care\\src\\services\\index.js": "34", "D:\\Akka\\preg-and-baby-care\\src\\services\\schemeService.js": "35", "D:\\Akka\\preg-and-baby-care\\src\\services\\authService.js": "36", "D:\\Akka\\preg-and-baby-care\\src\\services\\planService.js": "37", "D:\\Akka\\preg-and-baby-care\\src\\services\\nutritionService.js": "38", "D:\\Akka\\preg-and-baby-care\\src\\services\\exerciseService.js": "39", "D:\\Akka\\preg-and-baby-care\\src\\services\\weightTrackerService.js": "40", "D:\\Akka\\preg-and-baby-care\\src\\reportWebVitals.js": "41", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminNutrition.js": "42", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminExercise.js": "43", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js": "44", "D:\\Akka\\preg-and-baby-care\\src\\services\\dbService.js": "45", "D:\\Akka\\preg-and-baby-care\\src\\services\\supabaseClient.js": "46", "D:\\Akka\\preg-and-baby-care\\src\\config\\db.js": "47", "D:\\Akka\\preg-and-baby-care\\src\\services\\babyCareService.js": "48", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js": "49", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js": "50", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js": "51", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js": "52", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js": "53", "D:\\Akka\\preg-and-baby-care\\src\\components\\chatbot\\Chatbot.js": "54", "D:\\Akka\\preg-and-baby-care\\src\\services\\chatbotService.js": "55", "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminChatbot.js": "56", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\index.js": "57", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\App.js": "58", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\reportWebVitals.js": "59", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\context\\AuthContext.js": "60", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\LoginPage.js": "61", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyCare.js": "62", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\HomePage.js": "63", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Contact.js": "64", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SignupPage.js": "65", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js": "66", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Schemes.js": "67", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Nutrition.js": "68", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabySleep.js": "69", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyCare.js": "70", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Exercise.js": "71", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Appointments.js": "72", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\TermsOfService.js": "73", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\CookiePolicy.js": "74", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\WeightTracker.js": "75", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js": "76", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyNutrition.js": "77", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminNutrition.js": "78", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\NotFound.js": "79", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Vaccinations.js": "80", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyActivities.js": "81", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminExercise.js": "82", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js": "83", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js": "84", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js": "85", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js": "86", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js": "87", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSchemes.js": "88", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js": "89", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js": "90", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Navbar.js": "91", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Footer.js": "92", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\PageHeader.js": "93", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Tabs.js": "94", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\InfoCard.js": "95", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\TipCard.js": "96", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\index.js": "97", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\planService.js": "98", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\schemeService.js": "99", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\nutritionService.js": "100", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightTrackerService.js": "101", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\exerciseService.js": "102", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\authService.js": "103", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\babyCareService.js": "104", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\dbService.js": "105", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightService.js": "106", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\hooks\\use-toast.js": "107", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\button.jsx": "108", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\label.jsx": "109", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\input.jsx": "110", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\card.jsx": "111", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\chart.jsx": "112", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\lib\\utils.js": "113", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\chatbotService.js": "114", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Chatbot.js": "115", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\appointmentService.js": "116", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SleepPatterns.js": "117", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\utils\\helpers.js": "118", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\geminiService.js": "119", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyFAQ.js": "120", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\MealPlans.js": "121", "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\sqliteService.js": "122"}, {"size": 992, "mtime": 1746945169206, "results": "123", "hashOfConfig": "124"}, {"size": 4581, "mtime": 1747068972789, "results": "125", "hashOfConfig": "124"}, {"size": 2943, "mtime": 1747055493365, "results": "126", "hashOfConfig": "124"}, {"size": 7550, "mtime": 1746973547320, "results": "127", "hashOfConfig": "124"}, {"size": 4434, "mtime": 1746969318748, "results": "128", "hashOfConfig": "124"}, {"size": 17394, "mtime": 1747068922248, "results": "129", "hashOfConfig": "124"}, {"size": 2155, "mtime": 1746969292446, "results": "130", "hashOfConfig": "124"}, {"size": 3270, "mtime": 1746967763483, "results": "131", "hashOfConfig": "124"}, {"size": 3575, "mtime": 1746883645683, "results": "132", "hashOfConfig": "124"}, {"size": 496, "mtime": 1746880880576, "results": "133", "hashOfConfig": "124"}, {"size": 646, "mtime": 1746880909937, "results": "134", "hashOfConfig": "124"}, {"size": 407, "mtime": 1746880903944, "results": "135", "hashOfConfig": "124"}, {"size": 8154, "mtime": 1746940332820, "results": "136", "hashOfConfig": "124"}, {"size": 739, "mtime": 1746880898367, "results": "137", "hashOfConfig": "124"}, {"size": 10479, "mtime": 1746882930312, "results": "138", "hashOfConfig": "124"}, {"size": 6777, "mtime": 1746881897615, "results": "139", "hashOfConfig": "124"}, {"size": 11248, "mtime": 1746973519557, "results": "140", "hashOfConfig": "124"}, {"size": 12402, "mtime": 1746881949641, "results": "141", "hashOfConfig": "124"}, {"size": 8016, "mtime": 1746882001827, "results": "142", "hashOfConfig": "124"}, {"size": 560, "mtime": 1746966141891, "results": "143", "hashOfConfig": "124"}, {"size": 944, "mtime": 1746882159652, "results": "144", "hashOfConfig": "124"}, {"size": 264, "mtime": 1746882207797, "results": "145", "hashOfConfig": "124"}, {"size": 16882, "mtime": 1746884023296, "results": "146", "hashOfConfig": "124"}, {"size": 14253, "mtime": 1746969193187, "results": "147", "hashOfConfig": "124"}, {"size": 11243, "mtime": 1746973229592, "results": "148", "hashOfConfig": "124"}, {"size": 18677, "mtime": 1746973207296, "results": "149", "hashOfConfig": "124"}, {"size": 21927, "mtime": 1746884859498, "results": "150", "hashOfConfig": "124"}, {"size": 35356, "mtime": 1747069225381, "results": "151", "hashOfConfig": "124"}, {"size": 25442, "mtime": 1747069244168, "results": "152", "hashOfConfig": "124"}, {"size": 6636, "mtime": 1746889740424, "results": "153", "hashOfConfig": "124"}, {"size": 7926, "mtime": 1746889769945, "results": "154", "hashOfConfig": "124"}, {"size": 7514, "mtime": 1746889715640, "results": "155", "hashOfConfig": "124"}, {"size": 4520, "mtime": 1746966141070, "results": "156", "hashOfConfig": "124"}, {"size": 568, "mtime": 1747058595782, "results": "157", "hashOfConfig": "124"}, {"size": 3606, "mtime": 1747066381833, "results": "158", "hashOfConfig": "124"}, {"size": 3898, "mtime": 1747066203986, "results": "159", "hashOfConfig": "124"}, {"size": 2727, "mtime": 1747069297290, "results": "160", "hashOfConfig": "124"}, {"size": 4456, "mtime": 1747057786843, "results": "161", "hashOfConfig": "124"}, {"size": 8336, "mtime": 1747058279955, "results": "162", "hashOfConfig": "124"}, {"size": 3765, "mtime": 1747056969137, "results": "163", "hashOfConfig": "124"}, {"size": 362, "mtime": 1746967446669, "results": "164", "hashOfConfig": "124"}, {"size": 9380, "mtime": 1746968450856, "results": "165", "hashOfConfig": "124"}, {"size": 12288, "mtime": 1746968502689, "results": "166", "hashOfConfig": "124"}, {"size": 10579, "mtime": 1747057677261, "results": "167", "hashOfConfig": "124"}, {"size": 4539, "mtime": 1747057311281, "results": "168", "hashOfConfig": "124"}, {"size": 719, "mtime": 1747054728328, "results": "169", "hashOfConfig": "124"}, {"size": 1016, "mtime": 1747055159069, "results": "170", "hashOfConfig": "124"}, {"size": 12666, "mtime": 1747066740038, "results": "171", "hashOfConfig": "124"}, {"size": 12303, "mtime": 1747060294407, "results": "172", "hashOfConfig": "124"}, {"size": 11244, "mtime": 1747069176351, "results": "173", "hashOfConfig": "124"}, {"size": 13059, "mtime": 1747060427273, "results": "174", "hashOfConfig": "124"}, {"size": 5756, "mtime": 1747069010539, "results": "175", "hashOfConfig": "124"}, {"size": 11827, "mtime": 1747069205831, "results": "176", "hashOfConfig": "124"}, {"size": 5792, "mtime": 1747069161290, "results": "177", "hashOfConfig": "124"}, {"size": 12235, "mtime": 1747069115242, "results": "178", "hashOfConfig": "124"}, {"size": 8205, "mtime": 1747069320776, "results": "179", "hashOfConfig": "124"}, {"size": 992, "mtime": 1747834146064, "results": "180", "hashOfConfig": "181"}, {"size": 4820, "mtime": 1747981617964, "results": "182", "hashOfConfig": "181"}, {"size": 362, "mtime": 1747834146069, "results": "183", "hashOfConfig": "181"}, {"size": 2155, "mtime": 1747834146227, "results": "184", "hashOfConfig": "181"}, {"size": 2943, "mtime": 1747914817639, "results": "185", "hashOfConfig": "181"}, {"size": 11108, "mtime": 1747981669177, "results": "186", "hashOfConfig": "181"}, {"size": 7550, "mtime": 1747834146299, "results": "187", "hashOfConfig": "181"}, {"size": 8016, "mtime": 1747834146292, "results": "188", "hashOfConfig": "181"}, {"size": 4520, "mtime": 1747834146318, "results": "189", "hashOfConfig": "181"}, {"size": 13789, "mtime": 1752128925732, "results": "190", "hashOfConfig": "181"}, {"size": 6168, "mtime": 1747934604926, "results": "191", "hashOfConfig": "181"}, {"size": 8041, "mtime": 1747981571951, "results": "192", "hashOfConfig": "181"}, {"size": 6777, "mtime": 1747834146285, "results": "193", "hashOfConfig": "181"}, {"size": 17534, "mtime": 1747925828721, "results": "194", "hashOfConfig": "181"}, {"size": 7461, "mtime": 1747959747925, "results": "195", "hashOfConfig": "181"}, {"size": 35165, "mtime": 1747988557444, "results": "196", "hashOfConfig": "181"}, {"size": 9495, "mtime": 1747835445410, "results": "197", "hashOfConfig": "181"}, {"size": 9357, "mtime": 1747835522423, "results": "198", "hashOfConfig": "181"}, {"size": 62183, "mtime": 1747917882981, "results": "199", "hashOfConfig": "181"}, {"size": 7675, "mtime": 1747835477697, "results": "200", "hashOfConfig": "181"}, {"size": 9235, "mtime": 1747959375486, "results": "201", "hashOfConfig": "181"}, {"size": 9569, "mtime": 1747931113196, "results": "202", "hashOfConfig": "181"}, {"size": 944, "mtime": 1747834146304, "results": "203", "hashOfConfig": "181"}, {"size": 25344, "mtime": 1747834839709, "results": "204", "hashOfConfig": "181"}, {"size": 35258, "mtime": 1747834816845, "results": "205", "hashOfConfig": "181"}, {"size": 13841, "mtime": 1747959846467, "results": "206", "hashOfConfig": "181"}, {"size": 5674, "mtime": 1747834673594, "results": "207", "hashOfConfig": "181"}, {"size": 12359, "mtime": 1747931524761, "results": "208", "hashOfConfig": "181"}, {"size": 10579, "mtime": 1747834146274, "results": "209", "hashOfConfig": "181"}, {"size": 11415, "mtime": 1747926555793, "results": "210", "hashOfConfig": "181"}, {"size": 11126, "mtime": 1747959356535, "results": "211", "hashOfConfig": "181"}, {"size": 12000, "mtime": 1747934644949, "results": "212", "hashOfConfig": "181"}, {"size": 12689, "mtime": 1747932006745, "results": "213", "hashOfConfig": "181"}, {"size": 264, "mtime": 1747834146195, "results": "214", "hashOfConfig": "181"}, {"size": 3451, "mtime": 1752128808240, "results": "215", "hashOfConfig": "181"}, {"size": 3632, "mtime": 1747835547150, "results": "216", "hashOfConfig": "181"}, {"size": 496, "mtime": 1747834146189, "results": "217", "hashOfConfig": "181"}, {"size": 646, "mtime": 1747834146198, "results": "218", "hashOfConfig": "181"}, {"size": 739, "mtime": 1747834146179, "results": "219", "hashOfConfig": "181"}, {"size": 407, "mtime": 1747834146201, "results": "220", "hashOfConfig": "181"}, {"size": 701, "mtime": 1747920071398, "results": "221", "hashOfConfig": "181"}, {"size": 2727, "mtime": 1747834146356, "results": "222", "hashOfConfig": "181"}, {"size": 3606, "mtime": 1747834146359, "results": "223", "hashOfConfig": "181"}, {"size": 5867, "mtime": 1747963645570, "results": "224", "hashOfConfig": "181"}, {"size": 3765, "mtime": 1747834146363, "results": "225", "hashOfConfig": "181"}, {"size": 8414, "mtime": 1747935191418, "results": "226", "hashOfConfig": "181"}, {"size": 3939, "mtime": 1750169034317, "results": "227", "hashOfConfig": "181"}, {"size": 23019, "mtime": 1747960847452, "results": "228", "hashOfConfig": "181"}, {"size": 3134, "mtime": 1750168975435, "results": "229", "hashOfConfig": "181"}, {"size": 4495, "mtime": 1752130315905, "results": "230", "hashOfConfig": "181"}, {"size": 686, "mtime": 1747887634716, "results": "231", "hashOfConfig": "181"}, {"size": 1647, "mtime": 1747888030880, "results": "232", "hashOfConfig": "181"}, {"size": 526, "mtime": 1747888108882, "results": "233", "hashOfConfig": "181"}, {"size": 690, "mtime": 1747888093926, "results": "234", "hashOfConfig": "181"}, {"size": 1586, "mtime": 1747888271618, "results": "235", "hashOfConfig": "181"}, {"size": 861, "mtime": 1747888192878, "results": "236", "hashOfConfig": "181"}, {"size": 136, "mtime": 1747887639145, "results": "237", "hashOfConfig": "181"}, {"size": 9546, "mtime": 1747983720310, "results": "238", "hashOfConfig": "181"}, {"size": 6368, "mtime": 1747983704393, "results": "239", "hashOfConfig": "181"}, {"size": 11081, "mtime": 1747920916361, "results": "240", "hashOfConfig": "181"}, {"size": 16327, "mtime": 1747930955715, "results": "241", "hashOfConfig": "181"}, {"size": 1994, "mtime": 1747834146371, "results": "242", "hashOfConfig": "181"}, {"size": 5788, "mtime": 1747962377378, "results": "243", "hashOfConfig": "181"}, {"size": 14308, "mtime": 1747983586878, "results": "244", "hashOfConfig": "181"}, {"size": 60097, "mtime": 1747987649577, "results": "245", "hashOfConfig": "181"}, {"size": 9173, "mtime": 1752130088504, "results": "246", "hashOfConfig": "181"}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "100jhwu", {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1q0ycvk", {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Akka\\preg-and-baby-care\\src\\index.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\App.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\LoginPage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\HomePage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Schemes.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyCare.js", ["613"], [], "D:\\Akka\\preg-and-baby-care\\src\\context\\AuthContext.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Navbar.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\layout\\Footer.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PageHeader.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\Tabs.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\TipCard.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\api.js", ["614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633"], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\InfoCard.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\PregnancyCare.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabySleep.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSchemes.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Contact.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\PrivateRoute.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\NotFound.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Appointments.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\WeightTracker.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Nutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Exercise.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyNutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\BabyActivities.js", ["634"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\Vaccinations.js", ["635"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\TermsOfService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\CookiePolicy.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\SignupPage.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\index.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\schemeService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\authService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\planService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\nutritionService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\exerciseService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\weightTrackerService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\reportWebVitals.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminNutrition.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminExercise.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\dbService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\supabaseClient.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\config\\db.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\babyCareService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js", ["636"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js", ["637", "638", "639", "640", "641"], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js", ["642"], [], "D:\\Akka\\preg-and-baby-care\\src\\components\\chatbot\\Chatbot.js", ["643"], [], "D:\\Akka\\preg-and-baby-care\\src\\services\\chatbotService.js", [], [], "D:\\Akka\\preg-and-baby-care\\src\\pages\\AdminChatbot.js", ["644"], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SignupPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\ConsultDoctor.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Schemes.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Nutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabySleep.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Exercise.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Appointments.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\TermsOfService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\CookiePolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\WeightTracker.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PrivacyPolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\Vaccinations.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\BabyActivities.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminExercise.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyCare.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminVaccinations.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminWeightTracker.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSleepPatterns.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyNutrition.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminSchemes.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\AdminBabyActivities.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\ScrollToTop.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\PageHeader.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Tabs.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\InfoCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\TipCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\planService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\schemeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\nutritionService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightTrackerService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\exerciseService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\babyCareService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\dbService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\weightService.js", ["645", "646", "647", "648", "649"], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\hooks\\use-toast.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\card.jsx", [], ["650"], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\ui\\chart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\chatbotService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\components\\common\\Chatbot.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\appointmentService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\SleepPatterns.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\utils\\helpers.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\geminiService.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\PregnancyFAQ.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\pages\\MealPlans.js", [], [], "C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\src\\services\\sqliteService.js", [], [], {"ruleId": "651", "severity": 1, "message": "652", "line": 29, "column": 10, "nodeType": "653", "messageId": "654", "endLine": 29, "endColumn": 24}, {"ruleId": "655", "severity": 1, "message": "656", "line": 42, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 42, "endColumn": 58}, {"ruleId": "655", "severity": 1, "message": "656", "line": 62, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 62, "endColumn": 59}, {"ruleId": "655", "severity": 1, "message": "656", "line": 72, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 72, "endColumn": 59}, {"ruleId": "655", "severity": 1, "message": "656", "line": 82, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 82, "endColumn": 72}, {"ruleId": "655", "severity": 1, "message": "656", "line": 96, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 96, "endColumn": 69}, {"ruleId": "655", "severity": 1, "message": "656", "line": 111, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 111, "endColumn": 68}, {"ruleId": "655", "severity": 1, "message": "656", "line": 129, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 129, "endColumn": 66}, {"ruleId": "655", "severity": 1, "message": "656", "line": 140, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 140, "endColumn": 69}, {"ruleId": "655", "severity": 1, "message": "656", "line": 158, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 158, "endColumn": 66}, {"ruleId": "655", "severity": 1, "message": "656", "line": 170, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 170, "endColumn": 69}, {"ruleId": "655", "severity": 1, "message": "656", "line": 188, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 188, "endColumn": 66}, {"ruleId": "655", "severity": 1, "message": "656", "line": 196, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 196, "endColumn": 69}, {"ruleId": "655", "severity": 1, "message": "656", "line": 210, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 210, "endColumn": 67}, {"ruleId": "655", "severity": 1, "message": "656", "line": 225, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 225, "endColumn": 66}, {"ruleId": "655", "severity": 1, "message": "656", "line": 243, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 243, "endColumn": 64}, {"ruleId": "655", "severity": 1, "message": "656", "line": 254, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 254, "endColumn": 67}, {"ruleId": "655", "severity": 1, "message": "656", "line": 272, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 272, "endColumn": 64}, {"ruleId": "655", "severity": 1, "message": "656", "line": 284, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 284, "endColumn": 67}, {"ruleId": "655", "severity": 1, "message": "656", "line": 302, "column": 9, "nodeType": "657", "messageId": "658", "endLine": 302, "endColumn": 64}, {"ruleId": "655", "severity": 1, "message": "656", "line": 310, "column": 7, "nodeType": "657", "messageId": "658", "endLine": 310, "endColumn": 67}, {"ruleId": "659", "severity": 1, "message": "660", "line": 19, "column": 9, "nodeType": "661", "messageId": "662", "endLine": 21, "endColumn": 4}, {"ruleId": "659", "severity": 1, "message": "660", "line": 17, "column": 9, "nodeType": "661", "messageId": "662", "endLine": 19, "endColumn": 4}, {"ruleId": "659", "severity": 1, "message": "660", "line": 22, "column": 9, "nodeType": "661", "messageId": "662", "endLine": 24, "endColumn": 4}, {"ruleId": "651", "severity": 1, "message": "663", "line": 10, "column": 3, "nodeType": "653", "messageId": "654", "endLine": 10, "endColumn": 9}, {"ruleId": "651", "severity": 1, "message": "664", "line": 21, "column": 10, "nodeType": "653", "messageId": "654", "endLine": 21, "endColumn": 17}, {"ruleId": "651", "severity": 1, "message": "665", "line": 21, "column": 19, "nodeType": "653", "messageId": "654", "endLine": 21, "endColumn": 29}, {"ruleId": "651", "severity": 1, "message": "666", "line": 22, "column": 17, "nodeType": "653", "messageId": "654", "endLine": 22, "endColumn": 25}, {"ruleId": "651", "severity": 1, "message": "667", "line": 23, "column": 11, "nodeType": "653", "messageId": "654", "endLine": 23, "endColumn": 22}, {"ruleId": "659", "severity": 1, "message": "660", "line": 22, "column": 9, "nodeType": "661", "messageId": "662", "endLine": 24, "endColumn": 4}, {"ruleId": "668", "severity": 1, "message": "669", "line": 66, "column": 6, "nodeType": "670", "endLine": 66, "endColumn": 23, "suggestions": "671"}, {"ruleId": "659", "severity": 1, "message": "660", "line": 22, "column": 9, "nodeType": "661", "messageId": "662", "endLine": 24, "endColumn": 4}, {"ruleId": "672", "severity": 2, "message": "673", "line": 85, "column": 27, "nodeType": "653", "messageId": "674", "endLine": 85, "endColumn": 35}, {"ruleId": "672", "severity": 2, "message": "673", "line": 106, "column": 40, "nodeType": "653", "messageId": "674", "endLine": 106, "endColumn": 48}, {"ruleId": "672", "severity": 2, "message": "673", "line": 116, "column": 35, "nodeType": "653", "messageId": "674", "endLine": 116, "endColumn": 43}, {"ruleId": "672", "severity": 2, "message": "673", "line": 136, "column": 35, "nodeType": "653", "messageId": "674", "endLine": 136, "endColumn": 43}, {"ruleId": "672", "severity": 2, "message": "673", "line": 169, "column": 33, "nodeType": "653", "messageId": "674", "endLine": 169, "endColumn": 41}, {"ruleId": "675", "severity": 1, "message": "676", "line": 28, "column": 3, "nodeType": "677", "endLine": 35, "endColumn": 5, "suppressions": "678"}, "no-unused-vars", "'babyMilestones' is assigned a value but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "unexpected", "'faPlus' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'welcomeMessages'. Either include it or remove the dependency array.", "ArrayExpression", ["679"], "no-undef", "'supabase' is not defined.", "undef", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", ["680"], {"desc": "681", "fix": "682"}, {"kind": "683", "justification": "684"}, "Update the dependencies array to be: [isOpen, context, welcomeMessages]", {"range": "685", "text": "686"}, "directive", "", [1873, 1890], "[isOpen, context, welcomeMessages]"]