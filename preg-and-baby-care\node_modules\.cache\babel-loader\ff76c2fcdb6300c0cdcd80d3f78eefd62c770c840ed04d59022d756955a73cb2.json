{"ast": null, "code": "'use strict';\n\nvar hasSymbols = require('has-symbols/shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasToStringTagShams() {\n  return hasSymbols() && !!Symbol.toStringTag;\n};", "map": {"version": 3, "names": ["hasSymbols", "require", "module", "exports", "hasToStringTagShams", "Symbol", "toStringTag"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/has-tostringtag/shams.js"], "sourcesContent": ["'use strict';\n\nvar hasSymbols = require('has-symbols/shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasToStringTagShams() {\n\treturn hasSymbols() && !!Symbol.toStringTag;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,mBAAmB,CAAC;;AAE7C;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,mBAAmBA,CAAA,EAAG;EAC/C,OAAOJ,UAAU,CAAC,CAAC,IAAI,CAAC,CAACK,MAAM,CAACC,WAAW;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}