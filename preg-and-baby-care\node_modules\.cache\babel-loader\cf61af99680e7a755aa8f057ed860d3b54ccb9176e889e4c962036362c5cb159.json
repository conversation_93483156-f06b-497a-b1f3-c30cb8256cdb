{"ast": null, "code": "/**\n * @fileOverview Funnel Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Funnel } from '../numberAxis/Funnel';\nexport var FunnelChart = generateCategoricalChart({\n  chartName: 'FunnelChart',\n  GraphicalChild: Funnel,\n  validateTooltipEventTypes: ['item'],\n  defaultTooltipEventType: 'item',\n  axisComponents: [],\n  defaultProps: {\n    layout: 'centric'\n  }\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "Funnel", "FunnelChart", "chartName", "GraphicalChild", "validateTooltipEventTypes", "defaultTooltipEventType", "axisComponents", "defaultProps", "layout"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/recharts/es6/chart/FunnelChart.js"], "sourcesContent": ["/**\n * @fileOverview Funnel Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Funnel } from '../numberAxis/Funnel';\nexport var FunnelChart = generateCategoricalChart({\n  chartName: 'FunnelChart',\n  GraphicalChild: Funnel,\n  validateTooltipEventTypes: ['item'],\n  defaultTooltipEventType: 'item',\n  axisComponents: [],\n  defaultProps: {\n    layout: 'centric'\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAO,IAAIC,WAAW,GAAGF,wBAAwB,CAAC;EAChDG,SAAS,EAAE,aAAa;EACxBC,cAAc,EAAEH,MAAM;EACtBI,yBAAyB,EAAE,CAAC,MAAM,CAAC;EACnCC,uBAAuB,EAAE,MAAM;EAC/BC,cAAc,EAAE,EAAE;EAClBC,YAAY,EAAE;IACZC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}