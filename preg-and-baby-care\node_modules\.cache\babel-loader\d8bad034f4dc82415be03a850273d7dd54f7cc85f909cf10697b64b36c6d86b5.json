{"ast": null, "code": "'use strict';\n\nvar forEach = require('for-each');\nvar availableTypedArrays = require('available-typed-arrays');\nvar callBind = require('call-bind');\nvar callBound = require('call-bound');\nvar gOPD = require('gopd');\nvar getProto = require('get-proto');\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\nvar $slice = callBound('String.prototype.slice');\n\n/** @type {<T = unknown>(array: readonly T[], value: unknown) => number} */\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n  for (var i = 0; i < array.length; i += 1) {\n    if (array[i] === value) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n/** @typedef {import('./types').Getter} Getter */\n/** @type {import('./types').Cache} */\nvar cache = {\n  __proto__: null\n};\nif (hasToStringTag && gOPD && getProto) {\n  forEach(typedArrays, function (typedArray) {\n    var arr = new g[typedArray]();\n    if (Symbol.toStringTag in arr && getProto) {\n      var proto = getProto(arr);\n      // @ts-expect-error TS won't narrow inside a closure\n      var descriptor = gOPD(proto, Symbol.toStringTag);\n      if (!descriptor && proto) {\n        var superProto = getProto(proto);\n        // @ts-expect-error TS won't narrow inside a closure\n        descriptor = gOPD(superProto, Symbol.toStringTag);\n      }\n      // @ts-expect-error TODO: fix\n      cache['$' + typedArray] = callBind(descriptor.get);\n    }\n  });\n} else {\n  forEach(typedArrays, function (typedArray) {\n    var arr = new g[typedArray]();\n    var fn = arr.slice || arr.set;\n    if (fn) {\n      cache[(/** @type {`$${import('.').TypedArrayName}`} */'$' + typedArray)] = /** @type {import('./types').BoundSlice | import('./types').BoundSet} */\n      // @ts-expect-error TODO FIXME\n      callBind(fn);\n    }\n  });\n}\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n  /** @type {ReturnType<typeof tryAllTypedArrays>} */var found = false;\n  forEach(/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */cache, /** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */\n  function (getter, typedArray) {\n    if (!found) {\n      try {\n        // @ts-expect-error a throw is fine here\n        if ('$' + getter(value) === typedArray) {\n          found = /** @type {import('.').TypedArrayName} */$slice(typedArray, 1);\n        }\n      } catch (e) {/**/}\n    }\n  });\n  return found;\n};\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar trySlices = function tryAllSlices(value) {\n  /** @type {ReturnType<typeof tryAllSlices>} */var found = false;\n  forEach(/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */cache, /** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */function (getter, name) {\n    if (!found) {\n      try {\n        // @ts-expect-error a throw is fine here\n        getter(value);\n        found = /** @type {import('.').TypedArrayName} */$slice(name, 1);\n      } catch (e) {/**/}\n    }\n  });\n  return found;\n};\n\n/** @type {import('.')} */\nmodule.exports = function whichTypedArray(value) {\n  if (!value || typeof value !== 'object') {\n    return false;\n  }\n  if (!hasToStringTag) {\n    /** @type {string} */\n    var tag = $slice($toString(value), 8, -1);\n    if ($indexOf(typedArrays, tag) > -1) {\n      return tag;\n    }\n    if (tag !== 'Object') {\n      return false;\n    }\n    // node < 0.6 hits here on real Typed Arrays\n    return trySlices(value);\n  }\n  if (!gOPD) {\n    return null;\n  } // unknown engine\n  return tryTypedArrays(value);\n};", "map": {"version": 3, "names": ["for<PERSON>ach", "require", "availableTypedArrays", "callBind", "callBound", "gOPD", "getProto", "$toString", "hasToStringTag", "g", "globalThis", "global", "typedArrays", "$slice", "$indexOf", "indexOf", "array", "value", "i", "length", "cache", "__proto__", "typedArray", "arr", "Symbol", "toStringTag", "proto", "descriptor", "superProto", "get", "fn", "slice", "set", "tryTypedArrays", "tryAllTypedArrays", "found", "getter", "e", "trySlices", "tryAllSlices", "name", "module", "exports", "whichTypedArray", "tag"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/which-typed-array/index.js"], "sourcesContent": ["'use strict';\n\nvar forEach = require('for-each');\nvar availableTypedArrays = require('available-typed-arrays');\nvar callBind = require('call-bind');\nvar callBound = require('call-bound');\nvar gOPD = require('gopd');\nvar getProto = require('get-proto');\n\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $slice = callBound('String.prototype.slice');\n\n/** @type {<T = unknown>(array: readonly T[], value: unknown) => number} */\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n\tfor (var i = 0; i < array.length; i += 1) {\n\t\tif (array[i] === value) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n};\n\n/** @typedef {import('./types').Getter} Getter */\n/** @type {import('./types').Cache} */\nvar cache = { __proto__: null };\nif (hasToStringTag && gOPD && getProto) {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tif (Symbol.toStringTag in arr && getProto) {\n\t\t\tvar proto = getProto(arr);\n\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\tvar descriptor = gOPD(proto, Symbol.toStringTag);\n\t\t\tif (!descriptor && proto) {\n\t\t\t\tvar superProto = getProto(proto);\n\t\t\t\t// @ts-expect-error TS won't narrow inside a closure\n\t\t\t\tdescriptor = gOPD(superProto, Symbol.toStringTag);\n\t\t\t}\n\t\t\t// @ts-expect-error TODO: fix\n\t\t\tcache['$' + typedArray] = callBind(descriptor.get);\n\t\t}\n\t});\n} else {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tvar fn = arr.slice || arr.set;\n\t\tif (fn) {\n\t\t\tcache[\n\t\t\t\t/** @type {`$${import('.').TypedArrayName}`} */ ('$' + typedArray)\n\t\t\t] = /** @type {import('./types').BoundSlice | import('./types').BoundSet} */ (\n\t\t\t\t// @ts-expect-error TODO FIXME\n\t\t\t\tcallBind(fn)\n\t\t\t);\n\t\t}\n\t});\n}\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n\t/** @type {ReturnType<typeof tryAllTypedArrays>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */ (cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */\n\t\tfunction (getter, typedArray) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tif ('$' + getter(value) === typedArray) {\n\t\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(typedArray, 1));\n\t\t\t\t\t}\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {(value: object) => false | import('.').TypedArrayName} */\nvar trySlices = function tryAllSlices(value) {\n\t/** @type {ReturnType<typeof tryAllSlices>} */ var found = false;\n\tforEach(\n\t\t/** @type {Record<`\\$${import('.').TypedArrayName}`, Getter>} */(cache),\n\t\t/** @type {(getter: Getter, name: `\\$${import('.').TypedArrayName}`) => void} */ function (getter, name) {\n\t\t\tif (!found) {\n\t\t\t\ttry {\n\t\t\t\t\t// @ts-expect-error a throw is fine here\n\t\t\t\t\tgetter(value);\n\t\t\t\t\tfound = /** @type {import('.').TypedArrayName} */ ($slice(name, 1));\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t}\n\t);\n\treturn found;\n};\n\n/** @type {import('.')} */\nmodule.exports = function whichTypedArray(value) {\n\tif (!value || typeof value !== 'object') { return false; }\n\tif (!hasToStringTag) {\n\t\t/** @type {string} */\n\t\tvar tag = $slice($toString(value), 8, -1);\n\t\tif ($indexOf(typedArrays, tag) > -1) {\n\t\t\treturn tag;\n\t\t}\n\t\tif (tag !== 'Object') {\n\t\t\treturn false;\n\t\t}\n\t\t// node < 0.6 hits here on real Typed Arrays\n\t\treturn trySlices(value);\n\t}\n\tif (!gOPD) { return null; } // unknown engine\n\treturn tryTypedArrays(value);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAC5D,IAAIE,QAAQ,GAAGF,OAAO,CAAC,WAAW,CAAC;AACnC,IAAIG,SAAS,GAAGH,OAAO,CAAC,YAAY,CAAC;AACrC,IAAII,IAAI,GAAGJ,OAAO,CAAC,MAAM,CAAC;AAC1B,IAAIK,QAAQ,GAAGL,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIM,SAAS,GAAGH,SAAS,CAAC,2BAA2B,CAAC;AACtD,IAAII,cAAc,GAAGP,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC;AAEvD,IAAIQ,CAAC,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGC,MAAM,GAAGD,UAAU;AAC/D,IAAIE,WAAW,GAAGV,oBAAoB,CAAC,CAAC;AAExC,IAAIW,MAAM,GAAGT,SAAS,CAAC,wBAAwB,CAAC;;AAEhD;AACA,IAAIU,QAAQ,GAAGV,SAAS,CAAC,yBAAyB,EAAE,IAAI,CAAC,IAAI,SAASW,OAAOA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC3F,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIF,KAAK,CAACE,CAAC,CAAC,KAAKD,KAAK,EAAE;MACvB,OAAOC,CAAC;IACT;EACD;EACA,OAAO,CAAC,CAAC;AACV,CAAC;;AAED;AACA;AACA,IAAIE,KAAK,GAAG;EAAEC,SAAS,EAAE;AAAK,CAAC;AAC/B,IAAIb,cAAc,IAAIH,IAAI,IAAIC,QAAQ,EAAE;EACvCN,OAAO,CAACY,WAAW,EAAE,UAAUU,UAAU,EAAE;IAC1C,IAAIC,GAAG,GAAG,IAAId,CAAC,CAACa,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIE,MAAM,CAACC,WAAW,IAAIF,GAAG,IAAIjB,QAAQ,EAAE;MAC1C,IAAIoB,KAAK,GAAGpB,QAAQ,CAACiB,GAAG,CAAC;MACzB;MACA,IAAII,UAAU,GAAGtB,IAAI,CAACqB,KAAK,EAAEF,MAAM,CAACC,WAAW,CAAC;MAChD,IAAI,CAACE,UAAU,IAAID,KAAK,EAAE;QACzB,IAAIE,UAAU,GAAGtB,QAAQ,CAACoB,KAAK,CAAC;QAChC;QACAC,UAAU,GAAGtB,IAAI,CAACuB,UAAU,EAAEJ,MAAM,CAACC,WAAW,CAAC;MAClD;MACA;MACAL,KAAK,CAAC,GAAG,GAAGE,UAAU,CAAC,GAAGnB,QAAQ,CAACwB,UAAU,CAACE,GAAG,CAAC;IACnD;EACD,CAAC,CAAC;AACH,CAAC,MAAM;EACN7B,OAAO,CAACY,WAAW,EAAE,UAAUU,UAAU,EAAE;IAC1C,IAAIC,GAAG,GAAG,IAAId,CAAC,CAACa,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIQ,EAAE,GAAGP,GAAG,CAACQ,KAAK,IAAIR,GAAG,CAACS,GAAG;IAC7B,IAAIF,EAAE,EAAE;MACPV,KAAK,EACJ,+CAAiD,GAAG,GAAGE,UAAU,EACjE,GAAG;MACH;MACAnB,QAAQ,CAAC2B,EAAE,CACX;IACF;EACD,CAAC,CAAC;AACH;;AAEA;AACA,IAAIG,cAAc,GAAG,SAASC,iBAAiBA,CAACjB,KAAK,EAAE;EACtD,mDAAoD,IAAIkB,KAAK,GAAG,KAAK;EACrEnC,OAAO,CACN,gEAAkEoB,KAAK,EACvE;EACA,UAAUgB,MAAM,EAAEd,UAAU,EAAE;IAC7B,IAAI,CAACa,KAAK,EAAE;MACX,IAAI;QACH;QACA,IAAI,GAAG,GAAGC,MAAM,CAACnB,KAAK,CAAC,KAAKK,UAAU,EAAE;UACvCa,KAAK,GAAG,yCAA2CtB,MAAM,CAACS,UAAU,EAAE,CAAC,CAAE;QAC1E;MACD,CAAC,CAAC,OAAOe,CAAC,EAAE,CAAE;IACf;EACD,CACD,CAAC;EACD,OAAOF,KAAK;AACb,CAAC;;AAED;AACA,IAAIG,SAAS,GAAG,SAASC,YAAYA,CAACtB,KAAK,EAAE;EAC5C,8CAA+C,IAAIkB,KAAK,GAAG,KAAK;EAChEnC,OAAO,CACN,gEAAiEoB,KAAK,EACtE,gFAAiF,UAAUgB,MAAM,EAAEI,IAAI,EAAE;IACxG,IAAI,CAACL,KAAK,EAAE;MACX,IAAI;QACH;QACAC,MAAM,CAACnB,KAAK,CAAC;QACbkB,KAAK,GAAG,yCAA2CtB,MAAM,CAAC2B,IAAI,EAAE,CAAC,CAAE;MACpE,CAAC,CAAC,OAAOH,CAAC,EAAE,CAAE;IACf;EACD,CACD,CAAC;EACD,OAAOF,KAAK;AACb,CAAC;;AAED;AACAM,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAAC1B,KAAK,EAAE;EAChD,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EACzD,IAAI,CAACT,cAAc,EAAE;IACpB;IACA,IAAIoC,GAAG,GAAG/B,MAAM,CAACN,SAAS,CAACU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,IAAIH,QAAQ,CAACF,WAAW,EAAEgC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACpC,OAAOA,GAAG;IACX;IACA,IAAIA,GAAG,KAAK,QAAQ,EAAE;MACrB,OAAO,KAAK;IACb;IACA;IACA,OAAON,SAAS,CAACrB,KAAK,CAAC;EACxB;EACA,IAAI,CAACZ,IAAI,EAAE;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC;EAC5B,OAAO4B,cAAc,CAAChB,KAAK,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}