{"ast": null, "code": "// Import database service\nimport dbService from \"./dbService\";\n\n// Authentication services\nconst authService = {\n  login: async (usernameOrEmail, password) => {\n    try {\n      // Get users from the database\n      const users = await dbService.getAll(\"users\");\n\n      // Find user by username or email\n      const user = users.find(u => u.username === usernameOrEmail || u.email === usernameOrEmail);\n      if (!user) {\n        throw new Error(\"User not found\");\n      }\n\n      // Check password (in a real app, this would involve hashing)\n      if (user.password_hash !== password) {\n        throw new Error(\"Invalid password\");\n      }\n\n      // Generate a mock token\n      const token = `mock-token-${user.id}-${Date.now()}`;\n      return {\n        user: {\n          id: user.id,\n          username: user.username,\n          email: user.email,\n          isAdmin: user.role === \"admin\"\n        },\n        token\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Lo<PERSON> failed\");\n    }\n  },\n  signup: async (username, password, email) => {\n    try {\n      // Get users from the database\n      const users = await dbService.getAll(\"users\");\n\n      // Check if username or email already exists\n      const existingUser = users.find(u => u.username === username || u.email === email);\n      if (existingUser) {\n        if (existingUser.username === username) {\n          throw new Error(\"Username already taken\");\n        }\n        if (existingUser.email === email) {\n          throw new Error(\"Email already registered\");\n        }\n      }\n\n      // Create new user\n      const newUser = await dbService.create(\"users\", {\n        username,\n        email,\n        password_hash: password,\n        // In a real app, this would be hashed\n        role: \"user\" // Regular users are not admins by default\n      });\n\n      // Generate a mock token\n      const token = `mock-token-${newUser.id}-${Date.now()}`;\n      return {\n        user: {\n          id: newUser.id,\n          username: newUser.username,\n          email: newUser.email,\n          isAdmin: newUser.role === \"admin\"\n        },\n        token\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Signup failed\");\n    }\n  },\n  logout: async () => {\n    try {\n      // With JWT, we don't need to do anything server-side for logout\n      // Just remove the token from localStorage\n      localStorage.removeItem(\"token\");\n      return {\n        success: true\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Logout failed\");\n    }\n  },\n  getCurrentUser: async () => {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const storedUser = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n      if (!token || !storedUser) {\n        throw new Error(\"No token or user data provided\");\n      }\n\n      // Validate the user exists in our database\n      const users = await dbService.getAll(\"users\");\n      const user = users.find(u => u.id === storedUser.id);\n      if (!user) {\n        throw new Error(\"User not found\");\n      }\n      return {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        isAdmin: user.is_admin\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Failed to get current user\");\n    }\n  },\n  // Helper function to check if a user is an admin\n  isAdmin: async () => {\n    try {\n      const storedUser = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n      if (!storedUser) {\n        return false;\n      }\n\n      // Check if the user has admin privileges\n      return storedUser.isAdmin === true;\n    } catch (error) {\n      console.error(\"Error checking admin status:\", error);\n      return false; // On error, return false for security\n    }\n  },\n  // Helper function to get the auth token\n  getToken: () => {\n    return localStorage.getItem(\"token\");\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["dbService", "authService", "login", "usernameOrEmail", "password", "users", "getAll", "user", "find", "u", "username", "email", "Error", "password_hash", "token", "id", "Date", "now", "isAdmin", "role", "error", "message", "signup", "existingUser", "newUser", "create", "logout", "localStorage", "removeItem", "success", "getCurrentUser", "getItem", "storedUser", "JSON", "parse", "is_admin", "console", "getToken"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/authService.js"], "sourcesContent": ["// Import database service\nimport dbService from \"./dbService\";\n\n// Authentication services\nconst authService = {\n  login: async (usernameOrEmail, password) => {\n    try {\n      // Get users from the database\n      const users = await dbService.getAll(\"users\");\n\n      // Find user by username or email\n      const user = users.find(\n        (u) => u.username === usernameOrEmail || u.email === usernameOrEmail\n      );\n\n      if (!user) {\n        throw new Error(\"User not found\");\n      }\n\n      // Check password (in a real app, this would involve hashing)\n      if (user.password_hash !== password) {\n        throw new Error(\"Invalid password\");\n      }\n\n      // Generate a mock token\n      const token = `mock-token-${user.id}-${Date.now()}`;\n\n      return {\n        user: {\n          id: user.id,\n          username: user.username,\n          email: user.email,\n          isAdmin: user.role === \"admin\",\n        },\n        token,\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Login failed\");\n    }\n  },\n\n  signup: async (username, password, email) => {\n    try {\n      // Get users from the database\n      const users = await dbService.getAll(\"users\");\n\n      // Check if username or email already exists\n      const existingUser = users.find(\n        (u) => u.username === username || u.email === email\n      );\n\n      if (existingUser) {\n        if (existingUser.username === username) {\n          throw new Error(\"Username already taken\");\n        }\n        if (existingUser.email === email) {\n          throw new Error(\"Email already registered\");\n        }\n      }\n\n      // Create new user\n      const newUser = await dbService.create(\"users\", {\n        username,\n        email,\n        password_hash: password, // In a real app, this would be hashed\n        role: \"user\", // Regular users are not admins by default\n      });\n\n      // Generate a mock token\n      const token = `mock-token-${newUser.id}-${Date.now()}`;\n\n      return {\n        user: {\n          id: newUser.id,\n          username: newUser.username,\n          email: newUser.email,\n          isAdmin: newUser.role === \"admin\",\n        },\n        token,\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Signup failed\");\n    }\n  },\n\n  logout: async () => {\n    try {\n      // With JWT, we don't need to do anything server-side for logout\n      // Just remove the token from localStorage\n      localStorage.removeItem(\"token\");\n      return { success: true };\n    } catch (error) {\n      throw new Error(error.message || \"Logout failed\");\n    }\n  },\n\n  getCurrentUser: async () => {\n    try {\n      const token = localStorage.getItem(\"token\");\n      const storedUser = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n\n      if (!token || !storedUser) {\n        throw new Error(\"No token or user data provided\");\n      }\n\n      // Validate the user exists in our database\n      const users = await dbService.getAll(\"users\");\n      const user = users.find((u) => u.id === storedUser.id);\n\n      if (!user) {\n        throw new Error(\"User not found\");\n      }\n\n      return {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        isAdmin: user.is_admin,\n      };\n    } catch (error) {\n      throw new Error(error.message || \"Failed to get current user\");\n    }\n  },\n\n  // Helper function to check if a user is an admin\n  isAdmin: async () => {\n    try {\n      const storedUser = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n\n      if (!storedUser) {\n        return false;\n      }\n\n      // Check if the user has admin privileges\n      return storedUser.isAdmin === true;\n    } catch (error) {\n      console.error(\"Error checking admin status:\", error);\n      return false; // On error, return false for security\n    }\n  },\n\n  // Helper function to get the auth token\n  getToken: () => {\n    return localStorage.getItem(\"token\");\n  },\n};\n\nexport default authService;\n"], "mappings": "AAAA;AACA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,MAAAA,CAAOC,eAAe,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMC,KAAK,GAAG,MAAML,SAAS,CAACM,MAAM,CAAC,OAAO,CAAC;;MAE7C;MACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,IAAI,CACpBC,CAAC,IAAKA,CAAC,CAACC,QAAQ,KAAKP,eAAe,IAAIM,CAAC,CAACE,KAAK,KAAKR,eACvD,CAAC;MAED,IAAI,CAACI,IAAI,EAAE;QACT,MAAM,IAAIK,KAAK,CAAC,gBAAgB,CAAC;MACnC;;MAEA;MACA,IAAIL,IAAI,CAACM,aAAa,KAAKT,QAAQ,EAAE;QACnC,MAAM,IAAIQ,KAAK,CAAC,kBAAkB,CAAC;MACrC;;MAEA;MACA,MAAME,KAAK,GAAG,cAAcP,IAAI,CAACQ,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAEnD,OAAO;QACLV,IAAI,EAAE;UACJQ,EAAE,EAAER,IAAI,CAACQ,EAAE;UACXL,QAAQ,EAAEH,IAAI,CAACG,QAAQ;UACvBC,KAAK,EAAEJ,IAAI,CAACI,KAAK;UACjBO,OAAO,EAAEX,IAAI,CAACY,IAAI,KAAK;QACzB,CAAC;QACDL;MACF,CAAC;IACH,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd,MAAM,IAAIR,KAAK,CAACQ,KAAK,CAACC,OAAO,IAAI,cAAc,CAAC;IAClD;EACF,CAAC;EAEDC,MAAM,EAAE,MAAAA,CAAOZ,QAAQ,EAAEN,QAAQ,EAAEO,KAAK,KAAK;IAC3C,IAAI;MACF;MACA,MAAMN,KAAK,GAAG,MAAML,SAAS,CAACM,MAAM,CAAC,OAAO,CAAC;;MAE7C;MACA,MAAMiB,YAAY,GAAGlB,KAAK,CAACG,IAAI,CAC5BC,CAAC,IAAKA,CAAC,CAACC,QAAQ,KAAKA,QAAQ,IAAID,CAAC,CAACE,KAAK,KAAKA,KAChD,CAAC;MAED,IAAIY,YAAY,EAAE;QAChB,IAAIA,YAAY,CAACb,QAAQ,KAAKA,QAAQ,EAAE;UACtC,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QACA,IAAIW,YAAY,CAACZ,KAAK,KAAKA,KAAK,EAAE;UAChC,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;QAC7C;MACF;;MAEA;MACA,MAAMY,OAAO,GAAG,MAAMxB,SAAS,CAACyB,MAAM,CAAC,OAAO,EAAE;QAC9Cf,QAAQ;QACRC,KAAK;QACLE,aAAa,EAAET,QAAQ;QAAE;QACzBe,IAAI,EAAE,MAAM,CAAE;MAChB,CAAC,CAAC;;MAEF;MACA,MAAML,KAAK,GAAG,cAAcU,OAAO,CAACT,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAEtD,OAAO;QACLV,IAAI,EAAE;UACJQ,EAAE,EAAES,OAAO,CAACT,EAAE;UACdL,QAAQ,EAAEc,OAAO,CAACd,QAAQ;UAC1BC,KAAK,EAAEa,OAAO,CAACb,KAAK;UACpBO,OAAO,EAAEM,OAAO,CAACL,IAAI,KAAK;QAC5B,CAAC;QACDL;MACF,CAAC;IACH,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd,MAAM,IAAIR,KAAK,CAACQ,KAAK,CAACC,OAAO,IAAI,eAAe,CAAC;IACnD;EACF,CAAC;EAEDK,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,IAAI;MACF;MACA;MACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,MAAM,IAAIR,KAAK,CAACQ,KAAK,CAACC,OAAO,IAAI,eAAe,CAAC;IACnD;EACF,CAAC;EAEDS,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMhB,KAAK,GAAGa,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACP,YAAY,CAACI,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;MAErE,IAAI,CAACjB,KAAK,IAAI,CAACkB,UAAU,EAAE;QACzB,MAAM,IAAIpB,KAAK,CAAC,gCAAgC,CAAC;MACnD;;MAEA;MACA,MAAMP,KAAK,GAAG,MAAML,SAAS,CAACM,MAAM,CAAC,OAAO,CAAC;MAC7C,MAAMC,IAAI,GAAGF,KAAK,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,EAAE,KAAKiB,UAAU,CAACjB,EAAE,CAAC;MAEtD,IAAI,CAACR,IAAI,EAAE;QACT,MAAM,IAAIK,KAAK,CAAC,gBAAgB,CAAC;MACnC;MAEA,OAAO;QACLG,EAAE,EAAER,IAAI,CAACQ,EAAE;QACXL,QAAQ,EAAEH,IAAI,CAACG,QAAQ;QACvBC,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBO,OAAO,EAAEX,IAAI,CAAC4B;MAChB,CAAC;IACH,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAM,IAAIR,KAAK,CAACQ,KAAK,CAACC,OAAO,IAAI,4BAA4B,CAAC;IAChE;EACF,CAAC;EAED;EACAH,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,IAAI;MACF,MAAMc,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACP,YAAY,CAACI,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;MAErE,IAAI,CAACC,UAAU,EAAE;QACf,OAAO,KAAK;MACd;;MAEA;MACA,OAAOA,UAAU,CAACd,OAAO,KAAK,IAAI;IACpC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,KAAK,CAAC,CAAC;IAChB;EACF,CAAC;EAED;EACAiB,QAAQ,EAAEA,CAAA,KAAM;IACd,OAAOV,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;EACtC;AACF,CAAC;AAED,eAAe9B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}