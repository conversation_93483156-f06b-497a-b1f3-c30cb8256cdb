{"ast": null, "code": "import sqliteService from \"./sqliteService\";\n\n/**\n * Database service for interacting with SQLite\n * This replaces the Supabase implementation with a local SQLite database\n */\nconst dbService = {\n  /**\n   * Initialize the database tables if they don't exist\n   * This is called when the application starts\n   */\n  initializeDb: async () => {\n    console.log(\"Database initialized with SQLite\");\n    // SQLite tables are already created in sqliteService constructor\n  },\n  /**\n   * Get all records from a table\n   * @param {string} table - The table name\n   * @param {Object} filters - Optional filters to apply\n   * @returns {Promise<Array>} - Array of records\n   */\n  getAll: async (table, filters = {}) => {\n    try {\n      return sqliteService.getAll(table, filters);\n    } catch (error) {\n      console.error(`<PERSON>rror fetching all records from ${table}:`, error);\n      throw error;\n    }\n  },\n  /**\n   * Get a record by ID\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @returns {Promise<Object>} - The record\n   */\n  getById: async (table, id) => {\n    try {\n      return sqliteService.getById(table, id);\n    } catch (error) {\n      console.error(`Error fetching record with ID ${id} from ${table}:`, error);\n      throw error;\n    }\n  },\n  /**\n   * Find records by a field value\n   * @param {string} table - The table name\n   * @param {string} field - The field name\n   * @param {any} value - The value to match\n   * @returns {Promise<Array>} - Array of matching records\n   */\n  findBy: async (table, field, value) => {\n    try {\n      return sqliteService.findBy(table, field, value);\n    } catch (error) {\n      console.error(`Error finding records in ${table} where ${field} = ${value}:`, error);\n      throw error;\n    }\n  },\n  /**\n   * Create a new record\n   * @param {string} table - The table name\n   * @param {Object} data - The record data\n   * @returns {Promise<Object>} - The created record\n   */\n  create: async (table, data) => {\n    try {\n      return sqliteService.create(table, data);\n    } catch (error) {\n      console.error(`Error creating record in ${table}:`, error);\n      throw error;\n    }\n  },\n  /**\n   * Update a record\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @param {Object} data - The updated data\n   * @returns {Promise<Object>} - The updated record\n   */\n  update: async (table, id, data) => {\n    try {\n      return sqliteService.update(table, id, data);\n    } catch (error) {\n      console.error(`Error updating record with ID ${id} in ${table}:`, error);\n      throw error;\n    }\n  },\n  /**\n   * Delete a record\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @returns {Promise<boolean>} - Success status\n   */\n  delete: async (table, id) => {\n    try {\n      return sqliteService.delete(table, id);\n    } catch (error) {\n      console.error(`Error deleting record with ID ${id} from ${table}:`, error);\n      throw error;\n    }\n  }\n};\nexport default dbService;", "map": {"version": 3, "names": ["sqliteService", "dbService", "initializeDb", "console", "log", "getAll", "table", "filters", "error", "getById", "id", "find<PERSON><PERSON>", "field", "value", "create", "data", "update", "delete"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/dbService.js"], "sourcesContent": ["import sqliteService from \"./sqliteService\";\n\n/**\n * Database service for interacting with SQLite\n * This replaces the Supabase implementation with a local SQLite database\n */\nconst dbService = {\n  /**\n   * Initialize the database tables if they don't exist\n   * This is called when the application starts\n   */\n  initializeDb: async () => {\n    console.log(\"Database initialized with SQLite\");\n    // SQLite tables are already created in sqliteService constructor\n  },\n\n  /**\n   * Get all records from a table\n   * @param {string} table - The table name\n   * @param {Object} filters - Optional filters to apply\n   * @returns {Promise<Array>} - Array of records\n   */\n  getAll: async (table, filters = {}) => {\n    try {\n      return sqliteService.getAll(table, filters);\n    } catch (error) {\n      console.error(`<PERSON>rror fetching all records from ${table}:`, error);\n      throw error;\n    }\n  },\n\n  /**\n   * Get a record by ID\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @returns {Promise<Object>} - The record\n   */\n  getById: async (table, id) => {\n    try {\n      return sqliteService.getById(table, id);\n    } catch (error) {\n      console.error(\n        `Error fetching record with ID ${id} from ${table}:`,\n        error\n      );\n      throw error;\n    }\n  },\n\n  /**\n   * Find records by a field value\n   * @param {string} table - The table name\n   * @param {string} field - The field name\n   * @param {any} value - The value to match\n   * @returns {Promise<Array>} - Array of matching records\n   */\n  findBy: async (table, field, value) => {\n    try {\n      return sqliteService.findBy(table, field, value);\n    } catch (error) {\n      console.error(\n        `Error finding records in ${table} where ${field} = ${value}:`,\n        error\n      );\n      throw error;\n    }\n  },\n\n  /**\n   * Create a new record\n   * @param {string} table - The table name\n   * @param {Object} data - The record data\n   * @returns {Promise<Object>} - The created record\n   */\n  create: async (table, data) => {\n    try {\n      return sqliteService.create(table, data);\n    } catch (error) {\n      console.error(`Error creating record in ${table}:`, error);\n      throw error;\n    }\n  },\n\n  /**\n   * Update a record\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @param {Object} data - The updated data\n   * @returns {Promise<Object>} - The updated record\n   */\n  update: async (table, id, data) => {\n    try {\n      return sqliteService.update(table, id, data);\n    } catch (error) {\n      console.error(`Error updating record with ID ${id} in ${table}:`, error);\n      throw error;\n    }\n  },\n\n  /**\n   * Delete a record\n   * @param {string} table - The table name\n   * @param {number|string} id - The record ID\n   * @returns {Promise<boolean>} - Success status\n   */\n  delete: async (table, id) => {\n    try {\n      return sqliteService.delete(table, id);\n    } catch (error) {\n      console.error(\n        `Error deleting record with ID ${id} from ${table}:`,\n        error\n      );\n      throw error;\n    }\n  },\n};\n\nexport default dbService;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;EACEC,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxBC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,MAAM,EAAE,MAAAA,CAAOC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IACrC,IAAI;MACF,OAAOP,aAAa,CAACK,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC7C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,mCAAmCF,KAAK,GAAG,EAAEE,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,OAAO,EAAE,MAAAA,CAAOH,KAAK,EAAEI,EAAE,KAAK;IAC5B,IAAI;MACF,OAAOV,aAAa,CAACS,OAAO,CAACH,KAAK,EAAEI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CACX,iCAAiCE,EAAE,SAASJ,KAAK,GAAG,EACpDE,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEG,MAAM,EAAE,MAAAA,CAAOL,KAAK,EAAEM,KAAK,EAAEC,KAAK,KAAK;IACrC,IAAI;MACF,OAAOb,aAAa,CAACW,MAAM,CAACL,KAAK,EAAEM,KAAK,EAAEC,KAAK,CAAC;IAClD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CACX,4BAA4BF,KAAK,UAAUM,KAAK,MAAMC,KAAK,GAAG,EAC9DL,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEM,MAAM,EAAE,MAAAA,CAAOR,KAAK,EAAES,IAAI,KAAK;IAC7B,IAAI;MACF,OAAOf,aAAa,CAACc,MAAM,CAACR,KAAK,EAAES,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,4BAA4BF,KAAK,GAAG,EAAEE,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEQ,MAAM,EAAE,MAAAA,CAAOV,KAAK,EAAEI,EAAE,EAAEK,IAAI,KAAK;IACjC,IAAI;MACF,OAAOf,aAAa,CAACgB,MAAM,CAACV,KAAK,EAAEI,EAAE,EAAEK,IAAI,CAAC;IAC9C,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iCAAiCE,EAAE,OAAOJ,KAAK,GAAG,EAAEE,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACES,MAAM,EAAE,MAAAA,CAAOX,KAAK,EAAEI,EAAE,KAAK;IAC3B,IAAI;MACF,OAAOV,aAAa,CAACiB,MAAM,CAACX,KAAK,EAAEI,EAAE,CAAC;IACxC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CACX,iCAAiCE,EAAE,SAASJ,KAAK,GAAG,EACpDE,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}