{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.66 17.67a1.08 1.08 0 0 1-.04 1.6A12 12 0 0 1 4.73 2.38a1.1 1.1 0 0 1 1.61-.04z\",\n  key: \"4ite01\"\n}], [\"path\", {\n  d: \"M19.65 15.66A8 8 0 0 1 8.35 4.34\",\n  key: \"1gxipu\"\n}], [\"path\", {\n  d: \"m14 10-5.5 5.5\",\n  key: \"92pfem\"\n}], [\"path\", {\n  d: \"M14 17.85V10H6.15\",\n  key: \"xqmtsk\"\n}]];\nconst Citrus = createLucideIcon(\"citrus\", __iconNode);\nexport { __iconNode, Citrus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Citrus", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\node_modules\\lucide-react\\src\\icons\\citrus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21.66 17.67a1.08 1.08 0 0 1-.04 1.6A12 12 0 0 1 4.73 2.38a1.1 1.1 0 0 1 1.61-.04z',\n      key: '4ite01',\n    },\n  ],\n  ['path', { d: 'M19.65 15.66A8 8 0 0 1 8.35 4.34', key: '1gxipu' }],\n  ['path', { d: 'm14 10-5.5 5.5', key: '92pfem' }],\n  ['path', { d: 'M14 17.85V10H6.15', key: 'xqmtsk' }],\n];\n\n/**\n * @component @name Citrus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuNjYgMTcuNjdhMS4wOCAxLjA4IDAgMCAxLS4wNCAxLjZBMTIgMTIgMCAwIDEgNC43MyAyLjM4YTEuMSAxLjEgMCAwIDEgMS42MS0uMDR6IiAvPgogIDxwYXRoIGQ9Ik0xOS42NSAxNS42NkE4IDggMCAwIDEgOC4zNSA0LjM0IiAvPgogIDxwYXRoIGQ9Im0xNCAxMC01LjUgNS41IiAvPgogIDxwYXRoIGQ9Ik0xNCAxNy44NVYxMEg2LjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/citrus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Citrus = createLucideIcon('citrus', __iconNode);\n\nexport default Citrus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACpD;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}