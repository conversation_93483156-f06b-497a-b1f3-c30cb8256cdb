# Preg and Baby Care React Application

A React.js application for pregnancy and baby care information, with government schemes management functionality.

## Features

- User authentication system with JWT
- Baby care information and resources
- Pregnancy tracking and guidance
- Government schemes information
- Nutrition plans
- Exercise guides
- Weight tracking

## Setup Instructions

### Prerequisites

- Node.js 14.x or higher
- npm (Node package manager)

### Frontend Setup

1. Install dependencies:

   ```
   npm install
   ```

2. Database Setup

   The application uses SQLite3 for local data storage. No external database setup is required - the database will be automatically created and initialized when you first run the application.

3. Start the React development server:

   ```
   npm start
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

### Backend Setup

1. Install server dependencies:

   ```
   npm install express cors body-parser jsonwebtoken
   ```

2. Start the backend server:

   ```
   node server.js
   ```

   The server will run on port 3000.

## Authentication

The application uses JWT (JSON Web Tokens) for authentication. The default credentials are:

### Admin User

- Username: admin
- Password: admin123

### Regular User

- Username: user
- Password: user123

## Project Structure

```
preg-and-baby-care/
├── public/                # Public assets
├── src/                   # Source files
│   ├── assets/            # CSS and other assets
│   │   └── css/           # CSS files
│   ├── components/        # React components
│   │   ├── common/        # Common components
│   │   └── layout/        # Layout components
│   ├── context/           # Context providers
│   ├── hooks/             # Custom React hooks
│   ├── pages/             # Page components
│   ├── services/          # API services
│   └── utils/             # Utility functions
├── server.js              # Backend server
└── README.md              # This file
```

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

### `npm test`

Launches the test runner in the interactive watch mode.

### `npm run build`

Builds the app for production to the `build` folder.

## Database

This application uses SQLite3 for local data storage. The database features include:

- Local SQLite3 database with browser localStorage persistence
- Automatic table creation and initialization
- Support for all application features including:
  - User authentication and management
  - Pregnancy and baby care information
  - Government schemes data
  - Nutrition plans and meal tracking
  - Exercise guides and weight tracking
  - Doctor appointments and scheduling
  - Chat sessions and messaging
- No external database setup required

## User Features

Users can:

- View all government schemes
- Access scheme details and eligibility information
- Browse pregnancy and baby care information
- View nutrition plans
- Access exercise guides
- Track weight during pregnancy

## License

This project is licensed under the MIT License.
