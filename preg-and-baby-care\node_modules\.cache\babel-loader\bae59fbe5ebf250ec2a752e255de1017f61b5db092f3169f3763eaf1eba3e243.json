{"ast": null, "code": "import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user === null || user === void 0 ? void 0 : user.id;\n};\n\n// Save a weight entry to Supabase\nexport const saveWeightEntry = async entry => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\"\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n  const {\n    data,\n    error\n  } = await supabase.from(\"weight_entries\").insert([entryData]).select().single();\n  if (error) {\n    console.error(\"Weight Service - Supabase error:\", error);\n    console.error(\"Weight Service - Error details:\", {\n      message: error.message,\n      details: error.details,\n      hint: error.hint,\n      code: error.code\n    });\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n  console.log(\"Weight Service - Successfully saved entry:\", data);\n  return data;\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return [];\n  }\n  const {\n    data,\n    error\n  } = await supabase.from(\"weight_entries\").select(\"*\").eq(\"user_id\", userId).order(\"week\", {\n    ascending: true\n  });\n  if (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n  return data || [];\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async id => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n  const {\n    error\n  } = await supabase.from(\"weight_entries\").delete().eq(\"id\", id).eq(\"user_id\", userId);\n  if (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async info => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  // Check if pregnancy info already exists for this user\n  const {\n    data: existingInfo\n  } = await supabase.from(\"pregnancy_info\").select(\"*\").eq(\"user_id\", userId).maybeSingle();\n  let result;\n  if (existingInfo) {\n    // Update existing record\n    const {\n      data,\n      error\n    } = await supabase.from(\"pregnancy_info\").update({\n      pre_pregnancy_weight: info.pre_pregnancy_weight,\n      height_feet: info.height_feet,\n      height_inches: info.height_inches,\n      bmi_category: info.bmi_category\n    }).eq(\"user_id\", userId).select().single();\n    if (error) {\n      console.error(\"Error updating pregnancy info:\", error);\n      throw new Error(\"Failed to update pregnancy information\");\n    }\n    result = data;\n  } else {\n    // Insert new record\n    const {\n      data,\n      error\n    } = await supabase.from(\"pregnancy_info\").insert([{\n      user_id: userId,\n      pre_pregnancy_weight: info.pre_pregnancy_weight,\n      height_feet: info.height_feet,\n      height_inches: info.height_inches,\n      bmi_category: info.bmi_category\n    }]).select().single();\n    if (error) {\n      console.error(\"Error saving pregnancy info:\", error);\n      throw new Error(\"Failed to save pregnancy information\");\n    }\n    result = data;\n  }\n  return result;\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return null;\n  }\n  const {\n    data,\n    error\n  } = await supabase.from(\"pregnancy_info\").select(\"*\").eq(\"user_id\", userId).maybeSingle();\n  if (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n  return data;\n};", "map": {"version": 3, "names": ["dbService", "WeightEntry", "id", "user_id", "date", "weight", "week", "getUserId", "user", "JSON", "parse", "localStorage", "getItem", "console", "log", "saveWeightEntry", "entry", "userId", "error", "Error", "entryData", "type", "notes", "baby_name", "age_unit", "original_age", "data", "supabase", "from", "insert", "select", "single", "message", "details", "hint", "code", "getWeightEntries", "eq", "order", "ascending", "deleteWeightEntry", "delete", "savePregnancyInfo", "info", "existingInfo", "<PERSON><PERSON><PERSON><PERSON>", "result", "update", "pre_pregnancy_weight", "height_feet", "height_inches", "bmi_category", "getPregnancyInfo"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/weightService.js"], "sourcesContent": ["import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0,\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user?.id;\n};\n\n// Save a weight entry to Supabase\nexport const saveWeightEntry = async (entry) => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\",\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n\n  const { data, error } = await supabase\n    .from(\"weight_entries\")\n    .insert([entryData])\n    .select()\n    .single();\n\n  if (error) {\n    console.error(\"Weight Service - Supabase error:\", error);\n    console.error(\"Weight Service - Error details:\", {\n      message: error.message,\n      details: error.details,\n      hint: error.hint,\n      code: error.code,\n    });\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n\n  console.log(\"Weight Service - Successfully saved entry:\", data);\n  return data;\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return [];\n  }\n\n  const { data, error } = await supabase\n    .from(\"weight_entries\")\n    .select(\"*\")\n    .eq(\"user_id\", userId)\n    .order(\"week\", { ascending: true });\n\n  if (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n\n  return data || [];\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async (id) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  const { error } = await supabase\n    .from(\"weight_entries\")\n    .delete()\n    .eq(\"id\", id)\n    .eq(\"user_id\", userId);\n\n  if (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async (info) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  // Check if pregnancy info already exists for this user\n  const { data: existingInfo } = await supabase\n    .from(\"pregnancy_info\")\n    .select(\"*\")\n    .eq(\"user_id\", userId)\n    .maybeSingle();\n\n  let result;\n\n  if (existingInfo) {\n    // Update existing record\n    const { data, error } = await supabase\n      .from(\"pregnancy_info\")\n      .update({\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category,\n      })\n      .eq(\"user_id\", userId)\n      .select()\n      .single();\n\n    if (error) {\n      console.error(\"Error updating pregnancy info:\", error);\n      throw new Error(\"Failed to update pregnancy information\");\n    }\n\n    result = data;\n  } else {\n    // Insert new record\n    const { data, error } = await supabase\n      .from(\"pregnancy_info\")\n      .insert([\n        {\n          user_id: userId,\n          pre_pregnancy_weight: info.pre_pregnancy_weight,\n          height_feet: info.height_feet,\n          height_inches: info.height_inches,\n          bmi_category: info.bmi_category,\n        },\n      ])\n      .select()\n      .single();\n\n    if (error) {\n      console.error(\"Error saving pregnancy info:\", error);\n      throw new Error(\"Failed to save pregnancy information\");\n    }\n\n    result = data;\n  }\n\n  return result;\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return null;\n  }\n\n  const { data, error } = await supabase\n    .from(\"pregnancy_info\")\n    .select(\"*\")\n    .eq(\"user_id\", userId)\n    .maybeSingle();\n\n  if (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n\n  return data;\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,EAAE,EAAE,EAAE;EACNC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;EAC/DC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEN,IAAI,CAAC;EACrE,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMa,eAAe,GAAG,MAAOC,KAAK,IAAK;EAC9CH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEE,KAAK,CAAC;EAChE,MAAMC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAC1BM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,MAAM,CAAC;EAEhD,IAAI,CAACA,MAAM,EAAE;IACXJ,OAAO,CAACK,KAAK,CAAC,mCAAmC,CAAC;IAClD,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAMC,SAAS,GAAG;IAChBjB,OAAO,EAAEc,MAAM;IACfb,IAAI,EAAEY,KAAK,CAACZ,IAAI;IAChBC,MAAM,EAAEW,KAAK,CAACX,MAAM;IACpBC,IAAI,EAAEU,KAAK,CAACV,IAAI;IAChBe,IAAI,EAAEL,KAAK,CAACK,IAAI,IAAI;EACtB,CAAC;;EAED;EACA,IAAIL,KAAK,CAACM,KAAK,EAAEF,SAAS,CAACE,KAAK,GAAGN,KAAK,CAACM,KAAK;EAC9C,IAAIN,KAAK,CAACO,SAAS,EAAEH,SAAS,CAACG,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC1D,IAAIP,KAAK,CAACQ,QAAQ,EAAEJ,SAAS,CAACI,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EACvD,IAAIR,KAAK,CAACS,YAAY,EAAEL,SAAS,CAACK,YAAY,GAAGT,KAAK,CAACS,YAAY;EAEnEZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEM,SAAS,CAAC;EAE9D,MAAM;IAAEM,IAAI;IAAER;EAAM,CAAC,GAAG,MAAMS,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,CAACT,SAAS,CAAC,CAAC,CACnBU,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC;EAEX,IAAIb,KAAK,EAAE;IACTL,OAAO,CAACK,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxDL,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAE;MAC/Cc,OAAO,EAAEd,KAAK,CAACc,OAAO;MACtBC,OAAO,EAAEf,KAAK,CAACe,OAAO;MACtBC,IAAI,EAAEhB,KAAK,CAACgB,IAAI;MAChBC,IAAI,EAAEjB,KAAK,CAACiB;IACd,CAAC,CAAC;IACF,MAAM,IAAIhB,KAAK,CAAC,gCAAgCD,KAAK,CAACc,OAAO,EAAE,CAAC;EAClE;EAEAnB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,IAAI,CAAC;EAC/D,OAAOA,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMnB,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EAEA,MAAM;IAAES,IAAI;IAAER;EAAM,CAAC,GAAG,MAAMS,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBE,MAAM,CAAC,GAAG,CAAC,CACXO,EAAE,CAAC,SAAS,EAAEpB,MAAM,CAAC,CACrBqB,KAAK,CAAC,MAAM,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;EAErC,IAAIrB,KAAK,EAAE;IACTL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,OAAOO,IAAI,IAAI,EAAE;AACnB,CAAC;;AAED;AACA,OAAO,MAAMc,iBAAiB,GAAG,MAAOtC,EAAE,IAAK;EAC7C,MAAMe,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAM;IAAED;EAAM,CAAC,GAAG,MAAMS,QAAQ,CAC7BC,IAAI,CAAC,gBAAgB,CAAC,CACtBa,MAAM,CAAC,CAAC,CACRJ,EAAE,CAAC,IAAI,EAAEnC,EAAE,CAAC,CACZmC,EAAE,CAAC,SAAS,EAAEpB,MAAM,CAAC;EAExB,IAAIC,KAAK,EAAE;IACTL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;EAClD;AACF,CAAC;;AAED;AACA,OAAO,MAAMuB,iBAAiB,GAAG,MAAOC,IAAI,IAAK;EAC/C,MAAM1B,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;;EAEA;EACA,MAAM;IAAEO,IAAI,EAAEkB;EAAa,CAAC,GAAG,MAAMjB,QAAQ,CAC1CC,IAAI,CAAC,gBAAgB,CAAC,CACtBE,MAAM,CAAC,GAAG,CAAC,CACXO,EAAE,CAAC,SAAS,EAAEpB,MAAM,CAAC,CACrB4B,WAAW,CAAC,CAAC;EAEhB,IAAIC,MAAM;EAEV,IAAIF,YAAY,EAAE;IAChB;IACA,MAAM;MAAElB,IAAI;MAAER;IAAM,CAAC,GAAG,MAAMS,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBmB,MAAM,CAAC;MACNC,oBAAoB,EAAEL,IAAI,CAACK,oBAAoB;MAC/CC,WAAW,EAAEN,IAAI,CAACM,WAAW;MAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa;MACjCC,YAAY,EAAER,IAAI,CAACQ;IACrB,CAAC,CAAC,CACDd,EAAE,CAAC,SAAS,EAAEpB,MAAM,CAAC,CACrBa,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC;IAEX,IAAIb,KAAK,EAAE;MACTL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IAEA2B,MAAM,GAAGpB,IAAI;EACf,CAAC,MAAM;IACL;IACA,MAAM;MAAEA,IAAI;MAAER;IAAM,CAAC,GAAG,MAAMS,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,CACN;MACE1B,OAAO,EAAEc,MAAM;MACf+B,oBAAoB,EAAEL,IAAI,CAACK,oBAAoB;MAC/CC,WAAW,EAAEN,IAAI,CAACM,WAAW;MAC7BC,aAAa,EAAEP,IAAI,CAACO,aAAa;MACjCC,YAAY,EAAER,IAAI,CAACQ;IACrB,CAAC,CACF,CAAC,CACDrB,MAAM,CAAC,CAAC,CACRC,MAAM,CAAC,CAAC;IAEX,IAAIb,KAAK,EAAE;MACTL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;IACzD;IAEA2B,MAAM,GAAGpB,IAAI;EACf;EAEA,OAAOoB,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMnC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAM;IAAES,IAAI;IAAER;EAAM,CAAC,GAAG,MAAMS,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBE,MAAM,CAAC,GAAG,CAAC,CACXO,EAAE,CAAC,SAAS,EAAEpB,MAAM,CAAC,CACrB4B,WAAW,CAAC,CAAC;EAEhB,IAAI3B,KAAK,EAAE;IACTL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAEA,OAAOO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}