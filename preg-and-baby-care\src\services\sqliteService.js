import { v4 as uuidv4 } from "uuid";

// Browser-based SQLite implementation using localStorage as fallback
class SQLiteService {
  constructor() {
    this.db = null;
    this.tables = {};
    this.init();
  }

  init() {
    try {
      // Initialize in-memory database using localStorage for persistence
      this.loadFromStorage();

      // Initialize tables
      this.createTables();

      // Initialize sample data if this is the first run
      this.initializeSampleData();

      console.log("Local database initialized successfully");
    } catch (error) {
      console.error("Error initializing local database:", error);
      throw error;
    }
  }

  loadFromStorage() {
    try {
      const storedData = localStorage.getItem("sqlite_data");
      if (storedData) {
        this.tables = JSON.parse(storedData);
      }
    } catch (error) {
      console.warn("Could not load data from localStorage:", error);
      this.tables = {};
    }
  }

  saveToStorage() {
    try {
      localStorage.setItem("sqlite_data", JSON.stringify(this.tables));
    } catch (error) {
      console.warn("Could not save data to localStorage:", error);
    }
  }

  createTables() {
    // Initialize table structures if they don't exist
    const tableNames = [
      "users",
      "schemes",
      "plans",
      "nutrition_plans",
      "sleep_patterns",
      "baby_care_tips",
      "baby_activities",
      "baby_nutrition",
      "vaccinations",
      "baby_milestones",
      "chat_sessions",
      "chat_messages",
      "exercise_guides",
      "weight_entries",
      "pregnancy_info",
      "doctor_appointments",
      "faqs",
    ];

    tableNames.forEach((tableName) => {
      if (!this.tables[tableName]) {
        this.tables[tableName] = [];
      }
    });

    this.saveToStorage();
  }

  // Generic CRUD operations
  getAll(table, filters = {}) {
    try {
      if (!this.tables[table]) {
        return [];
      }

      let records = [...this.tables[table]];

      // Apply filters
      if (Object.keys(filters).length > 0) {
        records = records.filter((record) => {
          return Object.keys(filters).every((key) => {
            return record[key] === filters[key];
          });
        });
      }

      return records;
    } catch (error) {
      console.error(`Error fetching all records from ${table}:`, error);
      throw error;
    }
  }

  getById(table, id) {
    try {
      if (!this.tables[table]) {
        return null;
      }

      return this.tables[table].find((record) => record.id === id) || null;
    } catch (error) {
      console.error(
        `Error fetching record with ID ${id} from ${table}:`,
        error
      );
      throw error;
    }
  }

  findBy(table, field, value) {
    try {
      if (!this.tables[table]) {
        return [];
      }

      return this.tables[table].filter((record) => record[field] === value);
    } catch (error) {
      console.error(
        `Error finding records in ${table} where ${field} = ${value}:`,
        error
      );
      throw error;
    }
  }

  create(table, data) {
    try {
      if (!this.tables[table]) {
        this.tables[table] = [];
      }

      // Add ID and timestamps if not provided
      if (!data.id) {
        data.id = uuidv4();
      }
      if (!data.created_at) {
        data.created_at = new Date().toISOString();
      }
      if (!data.updated_at) {
        data.updated_at = new Date().toISOString();
      }

      // Create a copy of the data
      const newRecord = { ...data };

      // Add to table
      this.tables[table].push(newRecord);

      // Save to storage
      this.saveToStorage();

      // Return the created record
      return newRecord;
    } catch (error) {
      console.error(`Error creating record in ${table}:`, error);
      throw error;
    }
  }

  update(table, id, data) {
    try {
      if (!this.tables[table]) {
        throw new Error(`Table ${table} does not exist`);
      }

      const recordIndex = this.tables[table].findIndex(
        (record) => record.id === id
      );

      if (recordIndex === -1) {
        throw new Error(`No record found with ID ${id} in ${table}`);
      }

      // Add updated timestamp
      data.updated_at = new Date().toISOString();

      // Update the record
      this.tables[table][recordIndex] = {
        ...this.tables[table][recordIndex],
        ...data,
      };

      // Save to storage
      this.saveToStorage();

      // Return the updated record
      return this.tables[table][recordIndex];
    } catch (error) {
      console.error(`Error updating record with ID ${id} in ${table}:`, error);
      throw error;
    }
  }

  delete(table, id) {
    try {
      if (!this.tables[table]) {
        throw new Error(`Table ${table} does not exist`);
      }

      const recordIndex = this.tables[table].findIndex(
        (record) => record.id === id
      );

      if (recordIndex === -1) {
        throw new Error(`No record found with ID ${id} in ${table}`);
      }

      // Remove the record
      this.tables[table].splice(recordIndex, 1);

      // Save to storage
      this.saveToStorage();

      return true;
    } catch (error) {
      console.error(
        `Error deleting record with ID ${id} from ${table}:`,
        error
      );
      throw error;
    }
  }

  // Clear all data (useful for testing)
  clearAll() {
    this.tables = {};
    this.createTables();
  }

  // Export data (useful for backup)
  exportData() {
    return JSON.stringify(this.tables, null, 2);
  }

  // Import data (useful for restore)
  importData(jsonData) {
    try {
      this.tables = JSON.parse(jsonData);
      this.saveToStorage();
    } catch (error) {
      console.error("Error importing data:", error);
      throw error;
    }
  }

  // Initialize sample data for demonstration
  initializeSampleData() {
    try {
      // Check if sample data already exists
      if (this.tables.users && this.tables.users.length > 0) {
        console.log("Sample data already exists, skipping initialization");
        return;
      }

      console.log("Initializing sample data...");

      // Sample users
      const sampleUsers = [
        {
          id: uuidv4(),
          username: "admin",
          email: "<EMAIL>",
          password_hash: "admin123",
          role: "admin",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: uuidv4(),
          username: "user",
          email: "<EMAIL>",
          password_hash: "user123",
          role: "user",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      // Sample schemes
      const sampleSchemes = [
        {
          id: uuidv4(),
          title: "Pradhan Mantri Matru Vandana Yojana",
          description: "Cash incentive for pregnant and lactating mothers",
          eligibility: "Pregnant and lactating mothers for first live birth",
          benefits: "₹5,000 in three installments",
          how_to_apply: "Apply through Anganwadi centers or online portal",
          documents_required: "Aadhaar, Bank account, Pregnancy certificate",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: uuidv4(),
          title: "Janani Suraksha Yojana",
          description:
            "Safe motherhood intervention under National Health Mission",
          eligibility: "Pregnant women belonging to BPL families",
          benefits: "Cash assistance for institutional delivery",
          how_to_apply: "Through accredited health institutions",
          documents_required: "BPL card, Aadhaar, Bank account details",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      // Sample nutrition plans
      const sampleNutritionPlans = [
        {
          id: uuidv4(),
          title: "First Trimester Nutrition",
          description: "Essential nutrients for early pregnancy",
          content: "Focus on folic acid, iron, and calcium rich foods",
          trimester: "first",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: uuidv4(),
          title: "Second Trimester Nutrition",
          description: "Balanced diet for growing baby",
          content: "Increase protein and healthy fats intake",
          trimester: "second",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      // Add sample data to tables
      this.tables.users = sampleUsers;
      this.tables.schemes = sampleSchemes;
      this.tables.nutrition_plans = sampleNutritionPlans;

      // Save to storage
      this.saveToStorage();

      console.log("Sample data initialized successfully");
    } catch (error) {
      console.error("Error initializing sample data:", error);
    }
  }
}

// Create singleton instance
const sqliteService = new SQLiteService();

export default sqliteService;
