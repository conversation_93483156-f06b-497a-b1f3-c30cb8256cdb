{"ast": null, "code": "/**\n * Module dependencies.\n */\n\nvar fs = require('fs'),\n  path = require('path'),\n  fileURLToPath = require('file-uri-to-path'),\n  join = path.join,\n  dirname = path.dirname,\n  exists = fs.accessSync && function (path) {\n    try {\n      fs.accessSync(path);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  } || fs.existsSync || path.existsSync,\n  defaults = {\n    arrow: process.env.NODE_BINDINGS_ARROW || ' → ',\n    compiled: process.env.NODE_BINDINGS_COMPILED_DIR || 'compiled',\n    platform: process.platform,\n    arch: process.arch,\n    nodePreGyp: 'node-v' + process.versions.modules + '-' + process.platform + '-' + process.arch,\n    version: process.versions.node,\n    bindings: 'bindings.node',\n    try: [\n    // node-gyp's linked version in the \"build\" dir\n    ['module_root', 'build', 'bindings'],\n    // node-waf and gyp_addon (a.k.a node-gyp)\n    ['module_root', 'build', 'Debug', 'bindings'], ['module_root', 'build', 'Release', 'bindings'],\n    // Debug files, for development (legacy behavior, remove for node v0.9)\n    ['module_root', 'out', 'Debug', 'bindings'], ['module_root', 'Debug', 'bindings'],\n    // Release files, but manually compiled (legacy behavior, remove for node v0.9)\n    ['module_root', 'out', 'Release', 'bindings'], ['module_root', 'Release', 'bindings'],\n    // Legacy from node-waf, node <= 0.4.x\n    ['module_root', 'build', 'default', 'bindings'],\n    // Production \"Release\" buildtype binary (meh...)\n    ['module_root', 'compiled', 'version', 'platform', 'arch', 'bindings'],\n    // node-qbs builds\n    ['module_root', 'addon-build', 'release', 'install-root', 'bindings'], ['module_root', 'addon-build', 'debug', 'install-root', 'bindings'], ['module_root', 'addon-build', 'default', 'install-root', 'bindings'],\n    // node-pre-gyp path ./lib/binding/{node_abi}-{platform}-{arch}\n    ['module_root', 'lib', 'binding', 'nodePreGyp', 'bindings']]\n  };\n\n/**\n * The main `bindings()` function loads the compiled bindings for a given module.\n * It uses V8's Error API to determine the parent filename that this function is\n * being invoked from, which is then used to find the root directory.\n */\n\nfunction bindings(opts) {\n  // Argument surgery\n  if (typeof opts == 'string') {\n    opts = {\n      bindings: opts\n    };\n  } else if (!opts) {\n    opts = {};\n  }\n\n  // maps `defaults` onto `opts` object\n  Object.keys(defaults).map(function (i) {\n    if (!(i in opts)) opts[i] = defaults[i];\n  });\n\n  // Get the module root\n  if (!opts.module_root) {\n    opts.module_root = exports.getRoot(exports.getFileName());\n  }\n\n  // Ensure the given bindings name ends with .node\n  if (path.extname(opts.bindings) != '.node') {\n    opts.bindings += '.node';\n  }\n\n  // https://github.com/webpack/webpack/issues/4175#issuecomment-342931035\n  var requireFunc = typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require;\n  var tries = [],\n    i = 0,\n    l = opts.try.length,\n    n,\n    b,\n    err;\n  for (; i < l; i++) {\n    n = join.apply(null, opts.try[i].map(function (p) {\n      return opts[p] || p;\n    }));\n    tries.push(n);\n    try {\n      b = opts.path ? requireFunc.resolve(n) : requireFunc(n);\n      if (!opts.path) {\n        b.path = n;\n      }\n      return b;\n    } catch (e) {\n      if (e.code !== 'MODULE_NOT_FOUND' && e.code !== 'QUALIFIED_PATH_RESOLUTION_FAILED' && !/not find/i.test(e.message)) {\n        throw e;\n      }\n    }\n  }\n  err = new Error('Could not locate the bindings file. Tried:\\n' + tries.map(function (a) {\n    return opts.arrow + a;\n  }).join('\\n'));\n  err.tries = tries;\n  throw err;\n}\nmodule.exports = exports = bindings;\n\n/**\n * Gets the filename of the JavaScript file that invokes this function.\n * Used to help find the root directory of a module.\n * Optionally accepts an filename argument to skip when searching for the invoking filename\n */\n\nexports.getFileName = function getFileName(calling_file) {\n  var origPST = Error.prepareStackTrace,\n    origSTL = Error.stackTraceLimit,\n    dummy = {},\n    fileName;\n  Error.stackTraceLimit = 10;\n  Error.prepareStackTrace = function (e, st) {\n    for (var i = 0, l = st.length; i < l; i++) {\n      fileName = st[i].getFileName();\n      if (fileName !== __filename) {\n        if (calling_file) {\n          if (fileName !== calling_file) {\n            return;\n          }\n        } else {\n          return;\n        }\n      }\n    }\n  };\n\n  // run the 'prepareStackTrace' function above\n  Error.captureStackTrace(dummy);\n  dummy.stack;\n\n  // cleanup\n  Error.prepareStackTrace = origPST;\n  Error.stackTraceLimit = origSTL;\n\n  // handle filename that starts with \"file://\"\n  var fileSchema = 'file://';\n  if (fileName.indexOf(fileSchema) === 0) {\n    fileName = fileURLToPath(fileName);\n  }\n  return fileName;\n};\n\n/**\n * Gets the root directory of a module, given an arbitrary filename\n * somewhere in the module tree. The \"root directory\" is the directory\n * containing the `package.json` file.\n *\n *   In:  /home/<USER>/node-native-module/lib/index.js\n *   Out: /home/<USER>/node-native-module\n */\n\nexports.getRoot = function getRoot(file) {\n  var dir = dirname(file),\n    prev;\n  while (true) {\n    if (dir === '.') {\n      // Avoids an infinite loop in rare cases, like the REPL\n      dir = process.cwd();\n    }\n    if (exists(join(dir, 'package.json')) || exists(join(dir, 'node_modules'))) {\n      // Found the 'package.json' file or 'node_modules' dir; we're done\n      return dir;\n    }\n    if (prev === dir) {\n      // Got to the top\n      throw new Error('Could not find module root given file: \"' + file + '\". Do you have a `package.json` file? ');\n    }\n    // Try the parent dir next\n    prev = dir;\n    dir = join(dir, '..');\n  }\n};", "map": {"version": 3, "names": ["fs", "require", "path", "fileURLToPath", "join", "dirname", "exists", "accessSync", "e", "existsSync", "defaults", "arrow", "process", "env", "NODE_BINDINGS_ARROW", "compiled", "NODE_BINDINGS_COMPILED_DIR", "platform", "arch", "nodePreGyp", "versions", "modules", "version", "node", "bindings", "try", "opts", "Object", "keys", "map", "i", "module_root", "exports", "getRoot", "getFileName", "extname", "requireFunc", "__webpack_require__", "__non_webpack_require__", "tries", "l", "length", "n", "b", "err", "apply", "p", "push", "resolve", "code", "test", "message", "Error", "a", "module", "calling_file", "origPST", "prepareStackTrace", "origSTL", "stackTraceLimit", "dummy", "fileName", "st", "__filename", "captureStackTrace", "stack", "fileSchema", "indexOf", "file", "dir", "prev", "cwd"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/bindings/bindings.js"], "sourcesContent": ["/**\n * Module dependencies.\n */\n\nvar fs = require('fs'),\n  path = require('path'),\n  fileURLToPath = require('file-uri-to-path'),\n  join = path.join,\n  dirname = path.dirname,\n  exists =\n    (fs.accessSync &&\n      function(path) {\n        try {\n          fs.accessSync(path);\n        } catch (e) {\n          return false;\n        }\n        return true;\n      }) ||\n    fs.existsSync ||\n    path.existsSync,\n  defaults = {\n    arrow: process.env.NODE_BINDINGS_ARROW || ' → ',\n    compiled: process.env.NODE_BINDINGS_COMPILED_DIR || 'compiled',\n    platform: process.platform,\n    arch: process.arch,\n    nodePreGyp:\n      'node-v' +\n      process.versions.modules +\n      '-' +\n      process.platform +\n      '-' +\n      process.arch,\n    version: process.versions.node,\n    bindings: 'bindings.node',\n    try: [\n      // node-gyp's linked version in the \"build\" dir\n      ['module_root', 'build', 'bindings'],\n      // node-waf and gyp_addon (a.k.a node-gyp)\n      ['module_root', 'build', 'Debug', 'bindings'],\n      ['module_root', 'build', 'Release', 'bindings'],\n      // Debug files, for development (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Debug', 'bindings'],\n      ['module_root', 'Debug', 'bindings'],\n      // Release files, but manually compiled (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Release', 'bindings'],\n      ['module_root', 'Release', 'bindings'],\n      // Legacy from node-waf, node <= 0.4.x\n      ['module_root', 'build', 'default', 'bindings'],\n      // Production \"Release\" buildtype binary (meh...)\n      ['module_root', 'compiled', 'version', 'platform', 'arch', 'bindings'],\n      // node-qbs builds\n      ['module_root', 'addon-build', 'release', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'debug', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'default', 'install-root', 'bindings'],\n      // node-pre-gyp path ./lib/binding/{node_abi}-{platform}-{arch}\n      ['module_root', 'lib', 'binding', 'nodePreGyp', 'bindings']\n    ]\n  };\n\n/**\n * The main `bindings()` function loads the compiled bindings for a given module.\n * It uses V8's Error API to determine the parent filename that this function is\n * being invoked from, which is then used to find the root directory.\n */\n\nfunction bindings(opts) {\n  // Argument surgery\n  if (typeof opts == 'string') {\n    opts = { bindings: opts };\n  } else if (!opts) {\n    opts = {};\n  }\n\n  // maps `defaults` onto `opts` object\n  Object.keys(defaults).map(function(i) {\n    if (!(i in opts)) opts[i] = defaults[i];\n  });\n\n  // Get the module root\n  if (!opts.module_root) {\n    opts.module_root = exports.getRoot(exports.getFileName());\n  }\n\n  // Ensure the given bindings name ends with .node\n  if (path.extname(opts.bindings) != '.node') {\n    opts.bindings += '.node';\n  }\n\n  // https://github.com/webpack/webpack/issues/4175#issuecomment-342931035\n  var requireFunc =\n    typeof __webpack_require__ === 'function'\n      ? __non_webpack_require__\n      : require;\n\n  var tries = [],\n    i = 0,\n    l = opts.try.length,\n    n,\n    b,\n    err;\n\n  for (; i < l; i++) {\n    n = join.apply(\n      null,\n      opts.try[i].map(function(p) {\n        return opts[p] || p;\n      })\n    );\n    tries.push(n);\n    try {\n      b = opts.path ? requireFunc.resolve(n) : requireFunc(n);\n      if (!opts.path) {\n        b.path = n;\n      }\n      return b;\n    } catch (e) {\n      if (e.code !== 'MODULE_NOT_FOUND' &&\n          e.code !== 'QUALIFIED_PATH_RESOLUTION_FAILED' &&\n          !/not find/i.test(e.message)) {\n        throw e;\n      }\n    }\n  }\n\n  err = new Error(\n    'Could not locate the bindings file. Tried:\\n' +\n      tries\n        .map(function(a) {\n          return opts.arrow + a;\n        })\n        .join('\\n')\n  );\n  err.tries = tries;\n  throw err;\n}\nmodule.exports = exports = bindings;\n\n/**\n * Gets the filename of the JavaScript file that invokes this function.\n * Used to help find the root directory of a module.\n * Optionally accepts an filename argument to skip when searching for the invoking filename\n */\n\nexports.getFileName = function getFileName(calling_file) {\n  var origPST = Error.prepareStackTrace,\n    origSTL = Error.stackTraceLimit,\n    dummy = {},\n    fileName;\n\n  Error.stackTraceLimit = 10;\n\n  Error.prepareStackTrace = function(e, st) {\n    for (var i = 0, l = st.length; i < l; i++) {\n      fileName = st[i].getFileName();\n      if (fileName !== __filename) {\n        if (calling_file) {\n          if (fileName !== calling_file) {\n            return;\n          }\n        } else {\n          return;\n        }\n      }\n    }\n  };\n\n  // run the 'prepareStackTrace' function above\n  Error.captureStackTrace(dummy);\n  dummy.stack;\n\n  // cleanup\n  Error.prepareStackTrace = origPST;\n  Error.stackTraceLimit = origSTL;\n\n  // handle filename that starts with \"file://\"\n  var fileSchema = 'file://';\n  if (fileName.indexOf(fileSchema) === 0) {\n    fileName = fileURLToPath(fileName);\n  }\n\n  return fileName;\n};\n\n/**\n * Gets the root directory of a module, given an arbitrary filename\n * somewhere in the module tree. The \"root directory\" is the directory\n * containing the `package.json` file.\n *\n *   In:  /home/<USER>/node-native-module/lib/index.js\n *   Out: /home/<USER>/node-native-module\n */\n\nexports.getRoot = function getRoot(file) {\n  var dir = dirname(file),\n    prev;\n  while (true) {\n    if (dir === '.') {\n      // Avoids an infinite loop in rare cases, like the REPL\n      dir = process.cwd();\n    }\n    if (\n      exists(join(dir, 'package.json')) ||\n      exists(join(dir, 'node_modules'))\n    ) {\n      // Found the 'package.json' file or 'node_modules' dir; we're done\n      return dir;\n    }\n    if (prev === dir) {\n      // Got to the top\n      throw new Error(\n        'Could not find module root given file: \"' +\n          file +\n          '\". Do you have a `package.json` file? '\n      );\n    }\n    // Try the parent dir next\n    prev = dir;\n    dir = join(dir, '..');\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;EACpBC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;EACtBE,aAAa,GAAGF,OAAO,CAAC,kBAAkB,CAAC;EAC3CG,IAAI,GAAGF,IAAI,CAACE,IAAI;EAChBC,OAAO,GAAGH,IAAI,CAACG,OAAO;EACtBC,MAAM,GACHN,EAAE,CAACO,UAAU,IACZ,UAASL,IAAI,EAAE;IACb,IAAI;MACFF,EAAE,CAACO,UAAU,CAACL,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOM,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,IACHR,EAAE,CAACS,UAAU,IACbP,IAAI,CAACO,UAAU;EACjBC,QAAQ,GAAG;IACTC,KAAK,EAAEC,OAAO,CAACC,GAAG,CAACC,mBAAmB,IAAI,KAAK;IAC/CC,QAAQ,EAAEH,OAAO,CAACC,GAAG,CAACG,0BAA0B,IAAI,UAAU;IAC9DC,QAAQ,EAAEL,OAAO,CAACK,QAAQ;IAC1BC,IAAI,EAAEN,OAAO,CAACM,IAAI;IAClBC,UAAU,EACR,QAAQ,GACRP,OAAO,CAACQ,QAAQ,CAACC,OAAO,GACxB,GAAG,GACHT,OAAO,CAACK,QAAQ,GAChB,GAAG,GACHL,OAAO,CAACM,IAAI;IACdI,OAAO,EAAEV,OAAO,CAACQ,QAAQ,CAACG,IAAI;IAC9BC,QAAQ,EAAE,eAAe;IACzBC,GAAG,EAAE;IACH;IACA,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC;IACpC;IACA,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,EAC7C,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;IAC/C;IACA,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC,EAC3C,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC;IACpC;IACA,CAAC,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,EAC7C,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;IACtC;IACA,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;IAC/C;IACA,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACtE;IACA,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,EACrE,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,CAAC,EACnE,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC;IACrE;IACA,CAAC,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC;EAE/D,CAAC;;AAEH;AACA;AACA;AACA;AACA;;AAEA,SAASD,QAAQA,CAACE,IAAI,EAAE;EACtB;EACA,IAAI,OAAOA,IAAI,IAAI,QAAQ,EAAE;IAC3BA,IAAI,GAAG;MAAEF,QAAQ,EAAEE;IAAK,CAAC;EAC3B,CAAC,MAAM,IAAI,CAACA,IAAI,EAAE;IAChBA,IAAI,GAAG,CAAC,CAAC;EACX;;EAEA;EACAC,MAAM,CAACC,IAAI,CAAClB,QAAQ,CAAC,CAACmB,GAAG,CAAC,UAASC,CAAC,EAAE;IACpC,IAAI,EAAEA,CAAC,IAAIJ,IAAI,CAAC,EAAEA,IAAI,CAACI,CAAC,CAAC,GAAGpB,QAAQ,CAACoB,CAAC,CAAC;EACzC,CAAC,CAAC;;EAEF;EACA,IAAI,CAACJ,IAAI,CAACK,WAAW,EAAE;IACrBL,IAAI,CAACK,WAAW,GAAGC,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,WAAW,CAAC,CAAC,CAAC;EAC3D;;EAEA;EACA,IAAIhC,IAAI,CAACiC,OAAO,CAACT,IAAI,CAACF,QAAQ,CAAC,IAAI,OAAO,EAAE;IAC1CE,IAAI,CAACF,QAAQ,IAAI,OAAO;EAC1B;;EAEA;EACA,IAAIY,WAAW,GACb,OAAOC,mBAAmB,KAAK,UAAU,GACrCC,uBAAuB,GACvBrC,OAAO;EAEb,IAAIsC,KAAK,GAAG,EAAE;IACZT,CAAC,GAAG,CAAC;IACLU,CAAC,GAAGd,IAAI,CAACD,GAAG,CAACgB,MAAM;IACnBC,CAAC;IACDC,CAAC;IACDC,GAAG;EAEL,OAAOd,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;IACjBY,CAAC,GAAGtC,IAAI,CAACyC,KAAK,CACZ,IAAI,EACJnB,IAAI,CAACD,GAAG,CAACK,CAAC,CAAC,CAACD,GAAG,CAAC,UAASiB,CAAC,EAAE;MAC1B,OAAOpB,IAAI,CAACoB,CAAC,CAAC,IAAIA,CAAC;IACrB,CAAC,CACH,CAAC;IACDP,KAAK,CAACQ,IAAI,CAACL,CAAC,CAAC;IACb,IAAI;MACFC,CAAC,GAAGjB,IAAI,CAACxB,IAAI,GAAGkC,WAAW,CAACY,OAAO,CAACN,CAAC,CAAC,GAAGN,WAAW,CAACM,CAAC,CAAC;MACvD,IAAI,CAAChB,IAAI,CAACxB,IAAI,EAAE;QACdyC,CAAC,CAACzC,IAAI,GAAGwC,CAAC;MACZ;MACA,OAAOC,CAAC;IACV,CAAC,CAAC,OAAOnC,CAAC,EAAE;MACV,IAAIA,CAAC,CAACyC,IAAI,KAAK,kBAAkB,IAC7BzC,CAAC,CAACyC,IAAI,KAAK,kCAAkC,IAC7C,CAAC,WAAW,CAACC,IAAI,CAAC1C,CAAC,CAAC2C,OAAO,CAAC,EAAE;QAChC,MAAM3C,CAAC;MACT;IACF;EACF;EAEAoC,GAAG,GAAG,IAAIQ,KAAK,CACb,8CAA8C,GAC5Cb,KAAK,CACFV,GAAG,CAAC,UAASwB,CAAC,EAAE;IACf,OAAO3B,IAAI,CAACf,KAAK,GAAG0C,CAAC;EACvB,CAAC,CAAC,CACDjD,IAAI,CAAC,IAAI,CAChB,CAAC;EACDwC,GAAG,CAACL,KAAK,GAAGA,KAAK;EACjB,MAAMK,GAAG;AACX;AACAU,MAAM,CAACtB,OAAO,GAAGA,OAAO,GAAGR,QAAQ;;AAEnC;AACA;AACA;AACA;AACA;;AAEAQ,OAAO,CAACE,WAAW,GAAG,SAASA,WAAWA,CAACqB,YAAY,EAAE;EACvD,IAAIC,OAAO,GAAGJ,KAAK,CAACK,iBAAiB;IACnCC,OAAO,GAAGN,KAAK,CAACO,eAAe;IAC/BC,KAAK,GAAG,CAAC,CAAC;IACVC,QAAQ;EAEVT,KAAK,CAACO,eAAe,GAAG,EAAE;EAE1BP,KAAK,CAACK,iBAAiB,GAAG,UAASjD,CAAC,EAAEsD,EAAE,EAAE;IACxC,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEU,CAAC,GAAGsB,EAAE,CAACrB,MAAM,EAAEX,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;MACzC+B,QAAQ,GAAGC,EAAE,CAAChC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC9B,IAAI2B,QAAQ,KAAKE,UAAU,EAAE;QAC3B,IAAIR,YAAY,EAAE;UAChB,IAAIM,QAAQ,KAAKN,YAAY,EAAE;YAC7B;UACF;QACF,CAAC,MAAM;UACL;QACF;MACF;IACF;EACF,CAAC;;EAED;EACAH,KAAK,CAACY,iBAAiB,CAACJ,KAAK,CAAC;EAC9BA,KAAK,CAACK,KAAK;;EAEX;EACAb,KAAK,CAACK,iBAAiB,GAAGD,OAAO;EACjCJ,KAAK,CAACO,eAAe,GAAGD,OAAO;;EAE/B;EACA,IAAIQ,UAAU,GAAG,SAAS;EAC1B,IAAIL,QAAQ,CAACM,OAAO,CAACD,UAAU,CAAC,KAAK,CAAC,EAAE;IACtCL,QAAQ,GAAG1D,aAAa,CAAC0D,QAAQ,CAAC;EACpC;EAEA,OAAOA,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA7B,OAAO,CAACC,OAAO,GAAG,SAASA,OAAOA,CAACmC,IAAI,EAAE;EACvC,IAAIC,GAAG,GAAGhE,OAAO,CAAC+D,IAAI,CAAC;IACrBE,IAAI;EACN,OAAO,IAAI,EAAE;IACX,IAAID,GAAG,KAAK,GAAG,EAAE;MACf;MACAA,GAAG,GAAGzD,OAAO,CAAC2D,GAAG,CAAC,CAAC;IACrB;IACA,IACEjE,MAAM,CAACF,IAAI,CAACiE,GAAG,EAAE,cAAc,CAAC,CAAC,IACjC/D,MAAM,CAACF,IAAI,CAACiE,GAAG,EAAE,cAAc,CAAC,CAAC,EACjC;MACA;MACA,OAAOA,GAAG;IACZ;IACA,IAAIC,IAAI,KAAKD,GAAG,EAAE;MAChB;MACA,MAAM,IAAIjB,KAAK,CACb,0CAA0C,GACxCgB,IAAI,GACJ,wCACJ,CAAC;IACH;IACA;IACAE,IAAI,GAAGD,GAAG;IACVA,GAAG,GAAGjE,IAAI,CAACiE,GAAG,EAAE,IAAI,CAAC;EACvB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}