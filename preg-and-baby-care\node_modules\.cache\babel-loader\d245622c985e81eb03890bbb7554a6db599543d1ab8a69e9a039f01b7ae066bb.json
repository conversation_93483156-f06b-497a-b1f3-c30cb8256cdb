{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 20a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1Z\",\n  key: \"1pvr1r\"\n}], [\"path\", {\n  d: \"M20 16a8 8 0 1 0-16 0\",\n  key: \"1pa543\"\n}], [\"path\", {\n  d: \"M12 4v4\",\n  key: \"1bq03y\"\n}], [\"path\", {\n  d: \"M10 4h4\",\n  key: \"1xpv9s\"\n}]];\nconst ConciergeBell = createLucideIcon(\"concierge-bell\", __iconNode);\nexport { __iconNode, ConciergeBell as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ConciergeBell", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\node_modules\\lucide-react\\src\\icons\\concierge-bell.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M3 20a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1Z', key: '1pvr1r' },\n  ],\n  ['path', { d: 'M20 16a8 8 0 1 0-16 0', key: '1pa543' }],\n  ['path', { d: 'M12 4v4', key: '1bq03y' }],\n  ['path', { d: 'M10 4h4', key: '1xpv9s' }],\n];\n\n/**\n * @component @name ConciergeBell\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAyMGExIDEgMCAwIDEtMS0xdi0xYTIgMiAwIDAgMSAyLTJoMTZhMiAyIDAgMCAxIDIgMnYxYTEgMSAwIDAgMS0xIDFaIiAvPgogIDxwYXRoIGQ9Ik0yMCAxNmE4IDggMCAxIDAtMTYgMCIgLz4KICA8cGF0aCBkPSJNMTIgNHY0IiAvPgogIDxwYXRoIGQ9Ik0xMCA0aDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/concierge-bell\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ConciergeBell = createLucideIcon('concierge-bell', __iconNode);\n\nexport default ConciergeBell;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}