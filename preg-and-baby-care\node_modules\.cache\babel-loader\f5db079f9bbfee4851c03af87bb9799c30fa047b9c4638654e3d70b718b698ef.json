{"ast": null, "code": "var isNumber = require('./isNumber');\n\n/**\n * Checks if `value` is `NaN`.\n *\n * **Note:** This method is based on\n * [`Number.isNaN`](https://mdn.io/Number/isNaN) and is not the same as\n * global [`isNaN`](https://mdn.io/isNaN) which returns `true` for\n * `undefined` and other non-number values.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n * @example\n *\n * _.isNaN(NaN);\n * // => true\n *\n * _.isNaN(new Number(NaN));\n * // => true\n *\n * isNaN(undefined);\n * // => true\n *\n * _.isNaN(undefined);\n * // => false\n */\nfunction isNaN(value) {\n  // An `NaN` primitive is the only value that is not equal to itself.\n  // Perform the `toStringTag` check first to avoid errors with some\n  // ActiveX objects in IE.\n  return isNumber(value) && value != +value;\n}\nmodule.exports = isNaN;", "map": {"version": 3, "names": ["isNumber", "require", "isNaN", "value", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/lodash/isNaN.js"], "sourcesContent": ["var isNumber = require('./isNumber');\n\n/**\n * Checks if `value` is `NaN`.\n *\n * **Note:** This method is based on\n * [`Number.isNaN`](https://mdn.io/Number/isNaN) and is not the same as\n * global [`isNaN`](https://mdn.io/isNaN) which returns `true` for\n * `undefined` and other non-number values.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n * @example\n *\n * _.isNaN(NaN);\n * // => true\n *\n * _.isNaN(new Number(NaN));\n * // => true\n *\n * isNaN(undefined);\n * // => true\n *\n * _.isNaN(undefined);\n * // => false\n */\nfunction isNaN(value) {\n  // An `NaN` primitive is the only value that is not equal to itself.\n  // Perform the `toStringTag` check first to avoid errors with some\n  // ActiveX objects in IE.\n  return isNumber(value) && value != +value;\n}\n\nmodule.exports = isNaN;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB;EACA;EACA;EACA,OAAOH,QAAQ,CAACG,KAAK,CAAC,IAAIA,KAAK,IAAI,CAACA,KAAK;AAC3C;AAEAC,MAAM,CAACC,OAAO,GAAGH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}