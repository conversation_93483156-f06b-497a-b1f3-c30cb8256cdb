{"ast": null, "code": "import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user === null || user === void 0 ? void 0 : user.id;\n};\n\n// Save a weight entry to database\nexport const saveWeightEntry = async entry => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\"\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n  try {\n    const data = await dbService.create(\"weight_entries\", entryData);\n    console.log(\"Weight Service - Successfully saved entry:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Weight Service - Database error:\", error);\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return [];\n  }\n  try {\n    const entries = await dbService.getAll(\"weight_entries\", {\n      user_id: userId\n    });\n\n    // Sort by week (ascending)\n    return entries.sort((a, b) => a.week - b.week);\n  } catch (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async id => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n  try {\n    // First verify the entry belongs to the user\n    const entry = await dbService.getById(\"weight_entries\", id);\n    if (!entry || entry.user_id !== userId) {\n      throw new Error(\"Weight entry not found or access denied\");\n    }\n    await dbService.delete(\"weight_entries\", id);\n  } catch (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async info => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n  try {\n    // Check if pregnancy info already exists for this user\n    const existingInfoArray = await dbService.getAll(\"pregnancy_info\", {\n      user_id: userId\n    });\n    const existingInfo = existingInfoArray.length > 0 ? existingInfoArray[0] : null;\n    let result;\n    if (existingInfo) {\n      // Update existing record\n      result = await dbService.update(\"pregnancy_info\", existingInfo.id, {\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category,\n        updated_at: new Date().toISOString()\n      });\n    } else {\n      // Insert new record\n      result = await dbService.create(\"pregnancy_info\", {\n        user_id: userId,\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category\n      });\n    }\n    return result;\n  } catch (error) {\n    console.error(\"Error saving pregnancy info:\", error);\n    throw new Error(\"Failed to save pregnancy information\");\n  }\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return null;\n  }\n  try {\n    const pregnancyInfoArray = await dbService.getAll(\"pregnancy_info\", {\n      user_id: userId\n    });\n    return pregnancyInfoArray.length > 0 ? pregnancyInfoArray[0] : null;\n  } catch (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n};", "map": {"version": 3, "names": ["dbService", "WeightEntry", "id", "user_id", "date", "weight", "week", "getUserId", "user", "JSON", "parse", "localStorage", "getItem", "console", "log", "saveWeightEntry", "entry", "userId", "error", "Error", "entryData", "type", "notes", "baby_name", "age_unit", "original_age", "data", "create", "message", "getWeightEntries", "entries", "getAll", "sort", "a", "b", "deleteWeightEntry", "getById", "delete", "savePregnancyInfo", "info", "existingInfoArray", "existingInfo", "length", "result", "update", "pre_pregnancy_weight", "height_feet", "height_inches", "bmi_category", "updated_at", "Date", "toISOString", "getPregnancyInfo", "pregnancyInfoArray"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/weightService.js"], "sourcesContent": ["import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0,\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user?.id;\n};\n\n// Save a weight entry to database\nexport const saveWeightEntry = async (entry) => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\",\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n\n  try {\n    const data = await dbService.create(\"weight_entries\", entryData);\n    console.log(\"Weight Service - Successfully saved entry:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Weight Service - Database error:\", error);\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return [];\n  }\n\n  try {\n    const entries = await dbService.getAll(\"weight_entries\", {\n      user_id: userId,\n    });\n\n    // Sort by week (ascending)\n    return entries.sort((a, b) => a.week - b.week);\n  } catch (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async (id) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  try {\n    // First verify the entry belongs to the user\n    const entry = await dbService.getById(\"weight_entries\", id);\n    if (!entry || entry.user_id !== userId) {\n      throw new Error(\"Weight entry not found or access denied\");\n    }\n\n    await dbService.delete(\"weight_entries\", id);\n  } catch (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async (info) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  try {\n    // Check if pregnancy info already exists for this user\n    const existingInfoArray = await dbService.getAll(\"pregnancy_info\", {\n      user_id: userId,\n    });\n    const existingInfo =\n      existingInfoArray.length > 0 ? existingInfoArray[0] : null;\n\n    let result;\n\n    if (existingInfo) {\n      // Update existing record\n      result = await dbService.update(\"pregnancy_info\", existingInfo.id, {\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category,\n        updated_at: new Date().toISOString(),\n      });\n    } else {\n      // Insert new record\n      result = await dbService.create(\"pregnancy_info\", {\n        user_id: userId,\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category,\n      });\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error saving pregnancy info:\", error);\n    throw new Error(\"Failed to save pregnancy information\");\n  }\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return null;\n  }\n\n  try {\n    const pregnancyInfoArray = await dbService.getAll(\"pregnancy_info\", {\n      user_id: userId,\n    });\n    return pregnancyInfoArray.length > 0 ? pregnancyInfoArray[0] : null;\n  } catch (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,EAAE,EAAE,EAAE;EACNC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;EAC/DC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEN,IAAI,CAAC;EACrE,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMa,eAAe,GAAG,MAAOC,KAAK,IAAK;EAC9CH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEE,KAAK,CAAC;EAChE,MAAMC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAC1BM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,MAAM,CAAC;EAEhD,IAAI,CAACA,MAAM,EAAE;IACXJ,OAAO,CAACK,KAAK,CAAC,mCAAmC,CAAC;IAClD,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAMC,SAAS,GAAG;IAChBjB,OAAO,EAAEc,MAAM;IACfb,IAAI,EAAEY,KAAK,CAACZ,IAAI;IAChBC,MAAM,EAAEW,KAAK,CAACX,MAAM;IACpBC,IAAI,EAAEU,KAAK,CAACV,IAAI;IAChBe,IAAI,EAAEL,KAAK,CAACK,IAAI,IAAI;EACtB,CAAC;;EAED;EACA,IAAIL,KAAK,CAACM,KAAK,EAAEF,SAAS,CAACE,KAAK,GAAGN,KAAK,CAACM,KAAK;EAC9C,IAAIN,KAAK,CAACO,SAAS,EAAEH,SAAS,CAACG,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC1D,IAAIP,KAAK,CAACQ,QAAQ,EAAEJ,SAAS,CAACI,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EACvD,IAAIR,KAAK,CAACS,YAAY,EAAEL,SAAS,CAACK,YAAY,GAAGT,KAAK,CAACS,YAAY;EAEnEZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEM,SAAS,CAAC;EAE9D,IAAI;IACF,MAAMM,IAAI,GAAG,MAAM1B,SAAS,CAAC2B,MAAM,CAAC,gBAAgB,EAAEP,SAAS,CAAC;IAChEP,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,IAAI,CAAC;IAC/D,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAM,IAAIC,KAAK,CAAC,gCAAgCD,KAAK,CAACU,OAAO,EAAE,CAAC;EAClE;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMZ,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EAEA,IAAI;IACF,MAAMa,OAAO,GAAG,MAAM9B,SAAS,CAAC+B,MAAM,CAAC,gBAAgB,EAAE;MACvD5B,OAAO,EAAEc;IACX,CAAC,CAAC;;IAEF;IACA,OAAOa,OAAO,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3B,IAAI,GAAG4B,CAAC,CAAC5B,IAAI,CAAC;EAChD,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EACnD;AACF,CAAC;;AAED;AACA,OAAO,MAAMgB,iBAAiB,GAAG,MAAOjC,EAAE,IAAK;EAC7C,MAAMe,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,IAAI;IACF;IACA,MAAMH,KAAK,GAAG,MAAMhB,SAAS,CAACoC,OAAO,CAAC,gBAAgB,EAAElC,EAAE,CAAC;IAC3D,IAAI,CAACc,KAAK,IAAIA,KAAK,CAACb,OAAO,KAAKc,MAAM,EAAE;MACtC,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,MAAMnB,SAAS,CAACqC,MAAM,CAAC,gBAAgB,EAAEnC,EAAE,CAAC;EAC9C,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;EAClD;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,iBAAiB,GAAG,MAAOC,IAAI,IAAK;EAC/C,MAAMtB,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,IAAI;IACF;IACA,MAAMqB,iBAAiB,GAAG,MAAMxC,SAAS,CAAC+B,MAAM,CAAC,gBAAgB,EAAE;MACjE5B,OAAO,EAAEc;IACX,CAAC,CAAC;IACF,MAAMwB,YAAY,GAChBD,iBAAiB,CAACE,MAAM,GAAG,CAAC,GAAGF,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;IAE5D,IAAIG,MAAM;IAEV,IAAIF,YAAY,EAAE;MAChB;MACAE,MAAM,GAAG,MAAM3C,SAAS,CAAC4C,MAAM,CAAC,gBAAgB,EAAEH,YAAY,CAACvC,EAAE,EAAE;QACjE2C,oBAAoB,EAAEN,IAAI,CAACM,oBAAoB;QAC/CC,WAAW,EAAEP,IAAI,CAACO,WAAW;QAC7BC,aAAa,EAAER,IAAI,CAACQ,aAAa;QACjCC,YAAY,EAAET,IAAI,CAACS,YAAY;QAC/BC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAR,MAAM,GAAG,MAAM3C,SAAS,CAAC2B,MAAM,CAAC,gBAAgB,EAAE;QAChDxB,OAAO,EAAEc,MAAM;QACf4B,oBAAoB,EAAEN,IAAI,CAACM,oBAAoB;QAC/CC,WAAW,EAAEP,IAAI,CAACO,WAAW;QAC7BC,aAAa,EAAER,IAAI,CAACQ,aAAa;QACjCC,YAAY,EAAET,IAAI,CAACS;MACrB,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC,CAAC,OAAOzB,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;AACF,CAAC;;AAED;AACA,OAAO,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMnC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,IAAI;IACF,MAAMoC,kBAAkB,GAAG,MAAMrD,SAAS,CAAC+B,MAAM,CAAC,gBAAgB,EAAE;MAClE5B,OAAO,EAAEc;IACX,CAAC,CAAC;IACF,OAAOoC,kBAAkB,CAACX,MAAM,GAAG,CAAC,GAAGW,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI;EACrE,CAAC,CAAC,OAAOnC,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}