{"ast": null, "code": "import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user === null || user === void 0 ? void 0 : user.id;\n};\n\n// Save a weight entry to Supabase\nexport const saveWeightEntry = async entry => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\"\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n  try {\n    const data = await dbService.create(\"weight_entries\", entryData);\n    console.log(\"Weight Service - Successfully saved entry:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Weight Service - Database error:\", error);\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return [];\n  }\n  try {\n    const entries = await dbService.getAll(\"weight_entries\", {\n      user_id: userId\n    });\n\n    // Sort by week (ascending)\n    return entries.sort((a, b) => a.week - b.week);\n  } catch (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async id => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n  try {\n    // First verify the entry belongs to the user\n    const entry = await dbService.getById(\"weight_entries\", id);\n    if (!entry || entry.user_id !== userId) {\n      throw new Error(\"Weight entry not found or access denied\");\n    }\n    await dbService.delete(\"weight_entries\", id);\n  } catch (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async info => {\n  const userId = getUserId();\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  // Check if pregnancy info already exists for this user\n  const {\n    data: existingInfo\n  } = await supabase.from(\"pregnancy_info\").select(\"*\").eq(\"user_id\", userId).maybeSingle();\n  let result;\n  if (existingInfo) {\n    // Update existing record\n    const {\n      data,\n      error\n    } = await supabase.from(\"pregnancy_info\").update({\n      pre_pregnancy_weight: info.pre_pregnancy_weight,\n      height_feet: info.height_feet,\n      height_inches: info.height_inches,\n      bmi_category: info.bmi_category\n    }).eq(\"user_id\", userId).select().single();\n    if (error) {\n      console.error(\"Error updating pregnancy info:\", error);\n      throw new Error(\"Failed to update pregnancy information\");\n    }\n    result = data;\n  } else {\n    // Insert new record\n    const {\n      data,\n      error\n    } = await supabase.from(\"pregnancy_info\").insert([{\n      user_id: userId,\n      pre_pregnancy_weight: info.pre_pregnancy_weight,\n      height_feet: info.height_feet,\n      height_inches: info.height_inches,\n      bmi_category: info.bmi_category\n    }]).select().single();\n    if (error) {\n      console.error(\"Error saving pregnancy info:\", error);\n      throw new Error(\"Failed to save pregnancy information\");\n    }\n    result = data;\n  }\n  return result;\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n  if (!userId) {\n    return null;\n  }\n  const {\n    data,\n    error\n  } = await supabase.from(\"pregnancy_info\").select(\"*\").eq(\"user_id\", userId).maybeSingle();\n  if (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n  return data;\n};", "map": {"version": 3, "names": ["dbService", "WeightEntry", "id", "user_id", "date", "weight", "week", "getUserId", "user", "JSON", "parse", "localStorage", "getItem", "console", "log", "saveWeightEntry", "entry", "userId", "error", "Error", "entryData", "type", "notes", "baby_name", "age_unit", "original_age", "data", "create", "message", "getWeightEntries", "entries", "getAll", "sort", "a", "b", "deleteWeightEntry", "getById", "delete", "savePregnancyInfo", "info", "existingInfo", "supabase", "from", "select", "eq", "<PERSON><PERSON><PERSON><PERSON>", "result", "update", "pre_pregnancy_weight", "height_feet", "height_inches", "bmi_category", "single", "insert", "getPregnancyInfo"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/weightService.js"], "sourcesContent": ["import dbService from \"./dbService\";\n\n// Define the WeightEntry type\nexport const WeightEntry = {\n  id: \"\",\n  user_id: \"\",\n  date: \"\",\n  weight: 0,\n  week: 0,\n};\n\n// Get the current user's ID\nconst getUserId = () => {\n  const user = JSON.parse(localStorage.getItem(\"user\") || \"null\");\n  console.log(\"Weight Service - Current user from localStorage:\", user);\n  return user?.id;\n};\n\n// Save a weight entry to Supabase\nexport const saveWeightEntry = async (entry) => {\n  console.log(\"Weight Service - Attempting to save entry:\", entry);\n  const userId = getUserId();\n  console.log(\"Weight Service - User ID:\", userId);\n\n  if (!userId) {\n    console.error(\"Weight Service - No user ID found\");\n    throw new Error(\"User not authenticated\");\n  }\n\n  const entryData = {\n    user_id: userId,\n    date: entry.date,\n    weight: entry.weight,\n    week: entry.week,\n    type: entry.type || \"pregnancy\",\n  };\n\n  // Add optional fields if they exist\n  if (entry.notes) entryData.notes = entry.notes;\n  if (entry.baby_name) entryData.baby_name = entry.baby_name;\n  if (entry.age_unit) entryData.age_unit = entry.age_unit;\n  if (entry.original_age) entryData.original_age = entry.original_age;\n\n  console.log(\"Weight Service - Entry data to save:\", entryData);\n\n  try {\n    const data = await dbService.create(\"weight_entries\", entryData);\n    console.log(\"Weight Service - Successfully saved entry:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Weight Service - Database error:\", error);\n    throw new Error(`Failed to save weight entry: ${error.message}`);\n  }\n};\n\n// Get all weight entries for the current user\nexport const getWeightEntries = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return [];\n  }\n\n  try {\n    const entries = await dbService.getAll(\"weight_entries\", {\n      user_id: userId,\n    });\n\n    // Sort by week (ascending)\n    return entries.sort((a, b) => a.week - b.week);\n  } catch (error) {\n    console.error(\"Error fetching weight entries:\", error);\n    throw new Error(\"Failed to fetch weight entries\");\n  }\n};\n\n// Delete a weight entry\nexport const deleteWeightEntry = async (id) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  try {\n    // First verify the entry belongs to the user\n    const entry = await dbService.getById(\"weight_entries\", id);\n    if (!entry || entry.user_id !== userId) {\n      throw new Error(\"Weight entry not found or access denied\");\n    }\n\n    await dbService.delete(\"weight_entries\", id);\n  } catch (error) {\n    console.error(\"Error deleting weight entry:\", error);\n    throw new Error(\"Failed to delete weight entry\");\n  }\n};\n\n// Save pregnancy info\nexport const savePregnancyInfo = async (info) => {\n  const userId = getUserId();\n\n  if (!userId) {\n    throw new Error(\"User not authenticated\");\n  }\n\n  // Check if pregnancy info already exists for this user\n  const { data: existingInfo } = await supabase\n    .from(\"pregnancy_info\")\n    .select(\"*\")\n    .eq(\"user_id\", userId)\n    .maybeSingle();\n\n  let result;\n\n  if (existingInfo) {\n    // Update existing record\n    const { data, error } = await supabase\n      .from(\"pregnancy_info\")\n      .update({\n        pre_pregnancy_weight: info.pre_pregnancy_weight,\n        height_feet: info.height_feet,\n        height_inches: info.height_inches,\n        bmi_category: info.bmi_category,\n      })\n      .eq(\"user_id\", userId)\n      .select()\n      .single();\n\n    if (error) {\n      console.error(\"Error updating pregnancy info:\", error);\n      throw new Error(\"Failed to update pregnancy information\");\n    }\n\n    result = data;\n  } else {\n    // Insert new record\n    const { data, error } = await supabase\n      .from(\"pregnancy_info\")\n      .insert([\n        {\n          user_id: userId,\n          pre_pregnancy_weight: info.pre_pregnancy_weight,\n          height_feet: info.height_feet,\n          height_inches: info.height_inches,\n          bmi_category: info.bmi_category,\n        },\n      ])\n      .select()\n      .single();\n\n    if (error) {\n      console.error(\"Error saving pregnancy info:\", error);\n      throw new Error(\"Failed to save pregnancy information\");\n    }\n\n    result = data;\n  }\n\n  return result;\n};\n\n// Get pregnancy info for the current user\nexport const getPregnancyInfo = async () => {\n  const userId = getUserId();\n\n  if (!userId) {\n    return null;\n  }\n\n  const { data, error } = await supabase\n    .from(\"pregnancy_info\")\n    .select(\"*\")\n    .eq(\"user_id\", userId)\n    .maybeSingle();\n\n  if (error) {\n    console.error(\"Error fetching pregnancy info:\", error);\n    throw new Error(\"Failed to fetch pregnancy information\");\n  }\n\n  return data;\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,EAAE,EAAE,EAAE;EACNC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;EAC/DC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEN,IAAI,CAAC;EACrE,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMa,eAAe,GAAG,MAAOC,KAAK,IAAK;EAC9CH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEE,KAAK,CAAC;EAChE,MAAMC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAC1BM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,MAAM,CAAC;EAEhD,IAAI,CAACA,MAAM,EAAE;IACXJ,OAAO,CAACK,KAAK,CAAC,mCAAmC,CAAC;IAClD,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAMC,SAAS,GAAG;IAChBjB,OAAO,EAAEc,MAAM;IACfb,IAAI,EAAEY,KAAK,CAACZ,IAAI;IAChBC,MAAM,EAAEW,KAAK,CAACX,MAAM;IACpBC,IAAI,EAAEU,KAAK,CAACV,IAAI;IAChBe,IAAI,EAAEL,KAAK,CAACK,IAAI,IAAI;EACtB,CAAC;;EAED;EACA,IAAIL,KAAK,CAACM,KAAK,EAAEF,SAAS,CAACE,KAAK,GAAGN,KAAK,CAACM,KAAK;EAC9C,IAAIN,KAAK,CAACO,SAAS,EAAEH,SAAS,CAACG,SAAS,GAAGP,KAAK,CAACO,SAAS;EAC1D,IAAIP,KAAK,CAACQ,QAAQ,EAAEJ,SAAS,CAACI,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;EACvD,IAAIR,KAAK,CAACS,YAAY,EAAEL,SAAS,CAACK,YAAY,GAAGT,KAAK,CAACS,YAAY;EAEnEZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEM,SAAS,CAAC;EAE9D,IAAI;IACF,MAAMM,IAAI,GAAG,MAAM1B,SAAS,CAAC2B,MAAM,CAAC,gBAAgB,EAAEP,SAAS,CAAC;IAChEP,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,IAAI,CAAC;IAC/D,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAM,IAAIC,KAAK,CAAC,gCAAgCD,KAAK,CAACU,OAAO,EAAE,CAAC;EAClE;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMZ,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EAEA,IAAI;IACF,MAAMa,OAAO,GAAG,MAAM9B,SAAS,CAAC+B,MAAM,CAAC,gBAAgB,EAAE;MACvD5B,OAAO,EAAEc;IACX,CAAC,CAAC;;IAEF;IACA,OAAOa,OAAO,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3B,IAAI,GAAG4B,CAAC,CAAC5B,IAAI,CAAC;EAChD,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EACnD;AACF,CAAC;;AAED;AACA,OAAO,MAAMgB,iBAAiB,GAAG,MAAOjC,EAAE,IAAK;EAC7C,MAAMe,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,IAAI;IACF;IACA,MAAMH,KAAK,GAAG,MAAMhB,SAAS,CAACoC,OAAO,CAAC,gBAAgB,EAAElC,EAAE,CAAC;IAC3D,IAAI,CAACc,KAAK,IAAIA,KAAK,CAACb,OAAO,KAAKc,MAAM,EAAE;MACtC,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,MAAMnB,SAAS,CAACqC,MAAM,CAAC,gBAAgB,EAAEnC,EAAE,CAAC;EAC9C,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;EAClD;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,iBAAiB,GAAG,MAAOC,IAAI,IAAK;EAC/C,MAAMtB,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;EAC3C;;EAEA;EACA,MAAM;IAAEO,IAAI,EAAEc;EAAa,CAAC,GAAG,MAAMC,QAAQ,CAC1CC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAE3B,MAAM,CAAC,CACrB4B,WAAW,CAAC,CAAC;EAEhB,IAAIC,MAAM;EAEV,IAAIN,YAAY,EAAE;IAChB;IACA,MAAM;MAAEd,IAAI;MAAER;IAAM,CAAC,GAAG,MAAMuB,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBK,MAAM,CAAC;MACNC,oBAAoB,EAAET,IAAI,CAACS,oBAAoB;MAC/CC,WAAW,EAAEV,IAAI,CAACU,WAAW;MAC7BC,aAAa,EAAEX,IAAI,CAACW,aAAa;MACjCC,YAAY,EAAEZ,IAAI,CAACY;IACrB,CAAC,CAAC,CACDP,EAAE,CAAC,SAAS,EAAE3B,MAAM,CAAC,CACrB0B,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;IAEX,IAAIlC,KAAK,EAAE;MACTL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IAEA2B,MAAM,GAAGpB,IAAI;EACf,CAAC,MAAM;IACL;IACA,MAAM;MAAEA,IAAI;MAAER;IAAM,CAAC,GAAG,MAAMuB,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBW,MAAM,CAAC,CACN;MACElD,OAAO,EAAEc,MAAM;MACf+B,oBAAoB,EAAET,IAAI,CAACS,oBAAoB;MAC/CC,WAAW,EAAEV,IAAI,CAACU,WAAW;MAC7BC,aAAa,EAAEX,IAAI,CAACW,aAAa;MACjCC,YAAY,EAAEZ,IAAI,CAACY;IACrB,CAAC,CACF,CAAC,CACDR,MAAM,CAAC,CAAC,CACRS,MAAM,CAAC,CAAC;IAEX,IAAIlC,KAAK,EAAE;MACTL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;IACzD;IAEA2B,MAAM,GAAGpB,IAAI;EACf;EAEA,OAAOoB,MAAM;AACf,CAAC;;AAED;AACA,OAAO,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMrC,MAAM,GAAGV,SAAS,CAAC,CAAC;EAE1B,IAAI,CAACU,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAM;IAAES,IAAI;IAAER;EAAM,CAAC,GAAG,MAAMuB,QAAQ,CACnCC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAE3B,MAAM,CAAC,CACrB4B,WAAW,CAAC,CAAC;EAEhB,IAAI3B,KAAK,EAAE;IACTL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAEA,OAAOO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}