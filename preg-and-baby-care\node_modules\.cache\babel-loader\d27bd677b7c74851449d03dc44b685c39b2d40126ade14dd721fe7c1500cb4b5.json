{"ast": null, "code": "import { v4 as uuidv4 } from \"uuid\";\n\n// Browser-based SQLite implementation using localStorage as fallback\nclass SQLiteService {\n  constructor() {\n    this.db = null;\n    this.tables = {};\n    this.init();\n  }\n  init() {\n    try {\n      // Initialize in-memory database using localStorage for persistence\n      this.loadFromStorage();\n\n      // Initialize tables\n      this.createTables();\n\n      // Initialize sample data if this is the first run\n      this.initializeSampleData();\n      console.log(\"Local database initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing local database:\", error);\n      throw error;\n    }\n  }\n  loadFromStorage() {\n    try {\n      const storedData = localStorage.getItem(\"sqlite_data\");\n      if (storedData) {\n        this.tables = JSON.parse(storedData);\n      }\n    } catch (error) {\n      console.warn(\"Could not load data from localStorage:\", error);\n      this.tables = {};\n    }\n  }\n  saveToStorage() {\n    try {\n      localStorage.setItem(\"sqlite_data\", JSON.stringify(this.tables));\n    } catch (error) {\n      console.warn(\"Could not save data to localStorage:\", error);\n    }\n  }\n  createTables() {\n    // Initialize table structures if they don't exist\n    const tableNames = [\"users\", \"schemes\", \"plans\", \"nutrition_plans\", \"sleep_patterns\", \"baby_care_tips\", \"baby_activities\", \"baby_nutrition\", \"vaccinations\", \"baby_milestones\", \"chat_sessions\", \"chat_messages\", \"exercise_guides\", \"weight_entries\", \"pregnancy_info\", \"doctor_appointments\", \"faqs\"];\n    tableNames.forEach(tableName => {\n      if (!this.tables[tableName]) {\n        this.tables[tableName] = [];\n      }\n    });\n    this.saveToStorage();\n  }\n\n  // Generic CRUD operations\n  getAll(table, filters = {}) {\n    try {\n      if (!this.tables[table]) {\n        return [];\n      }\n      let records = [...this.tables[table]];\n\n      // Apply filters\n      if (Object.keys(filters).length > 0) {\n        records = records.filter(record => {\n          return Object.keys(filters).every(key => {\n            return record[key] === filters[key];\n          });\n        });\n      }\n      return records;\n    } catch (error) {\n      console.error(`Error fetching all records from ${table}:`, error);\n      throw error;\n    }\n  }\n  getById(table, id) {\n    try {\n      if (!this.tables[table]) {\n        return null;\n      }\n      return this.tables[table].find(record => record.id === id) || null;\n    } catch (error) {\n      console.error(`Error fetching record with ID ${id} from ${table}:`, error);\n      throw error;\n    }\n  }\n  findBy(table, field, value) {\n    try {\n      if (!this.tables[table]) {\n        return [];\n      }\n      return this.tables[table].filter(record => record[field] === value);\n    } catch (error) {\n      console.error(`Error finding records in ${table} where ${field} = ${value}:`, error);\n      throw error;\n    }\n  }\n  create(table, data) {\n    try {\n      if (!this.tables[table]) {\n        this.tables[table] = [];\n      }\n\n      // Add ID and timestamps if not provided\n      if (!data.id) {\n        data.id = uuidv4();\n      }\n      if (!data.created_at) {\n        data.created_at = new Date().toISOString();\n      }\n      if (!data.updated_at) {\n        data.updated_at = new Date().toISOString();\n      }\n\n      // Create a copy of the data\n      const newRecord = {\n        ...data\n      };\n\n      // Add to table\n      this.tables[table].push(newRecord);\n\n      // Save to storage\n      this.saveToStorage();\n\n      // Return the created record\n      return newRecord;\n    } catch (error) {\n      console.error(`Error creating record in ${table}:`, error);\n      throw error;\n    }\n  }\n  update(table, id, data) {\n    try {\n      if (!this.tables[table]) {\n        throw new Error(`Table ${table} does not exist`);\n      }\n      const recordIndex = this.tables[table].findIndex(record => record.id === id);\n      if (recordIndex === -1) {\n        throw new Error(`No record found with ID ${id} in ${table}`);\n      }\n\n      // Add updated timestamp\n      data.updated_at = new Date().toISOString();\n\n      // Update the record\n      this.tables[table][recordIndex] = {\n        ...this.tables[table][recordIndex],\n        ...data\n      };\n\n      // Save to storage\n      this.saveToStorage();\n\n      // Return the updated record\n      return this.tables[table][recordIndex];\n    } catch (error) {\n      console.error(`Error updating record with ID ${id} in ${table}:`, error);\n      throw error;\n    }\n  }\n  delete(table, id) {\n    try {\n      if (!this.tables[table]) {\n        throw new Error(`Table ${table} does not exist`);\n      }\n      const recordIndex = this.tables[table].findIndex(record => record.id === id);\n      if (recordIndex === -1) {\n        throw new Error(`No record found with ID ${id} in ${table}`);\n      }\n\n      // Remove the record\n      this.tables[table].splice(recordIndex, 1);\n\n      // Save to storage\n      this.saveToStorage();\n      return true;\n    } catch (error) {\n      console.error(`Error deleting record with ID ${id} from ${table}:`, error);\n      throw error;\n    }\n  }\n\n  // Clear all data (useful for testing)\n  clearAll() {\n    this.tables = {};\n    this.createTables();\n  }\n\n  // Export data (useful for backup)\n  exportData() {\n    return JSON.stringify(this.tables, null, 2);\n  }\n\n  // Import data (useful for restore)\n  importData(jsonData) {\n    try {\n      this.tables = JSON.parse(jsonData);\n      this.saveToStorage();\n    } catch (error) {\n      console.error(\"Error importing data:\", error);\n      throw error;\n    }\n  }\n\n  // Initialize sample data for demonstration\n  initializeSampleData() {\n    try {\n      // Check if sample data already exists\n      if (this.tables.users && this.tables.users.length > 0) {\n        console.log(\"Sample data already exists, skipping initialization\");\n        return;\n      }\n      console.log(\"Initializing sample data...\");\n\n      // Sample users\n      const sampleUsers = [{\n        id: uuidv4(),\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        password_hash: \"admin123\",\n        role: \"admin\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: uuidv4(),\n        username: \"user\",\n        email: \"<EMAIL>\",\n        password_hash: \"user123\",\n        role: \"user\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }];\n\n      // Sample schemes\n      const sampleSchemes = [{\n        id: uuidv4(),\n        title: \"Pradhan Mantri Matru Vandana Yojana\",\n        description: \"Cash incentive for pregnant and lactating mothers\",\n        eligibility: \"Pregnant and lactating mothers for first live birth\",\n        benefits: \"₹5,000 in three installments\",\n        how_to_apply: \"Apply through Anganwadi centers or online portal\",\n        documents_required: \"Aadhaar, Bank account, Pregnancy certificate\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: uuidv4(),\n        title: \"Janani Suraksha Yojana\",\n        description: \"Safe motherhood intervention under National Health Mission\",\n        eligibility: \"Pregnant women belonging to BPL families\",\n        benefits: \"Cash assistance for institutional delivery\",\n        how_to_apply: \"Through accredited health institutions\",\n        documents_required: \"BPL card, Aadhaar, Bank account details\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }];\n\n      // Sample nutrition plans\n      const sampleNutritionPlans = [{\n        id: uuidv4(),\n        title: \"First Trimester Nutrition\",\n        description: \"Essential nutrients for early pregnancy\",\n        content: \"Focus on folic acid, iron, and calcium rich foods\",\n        trimester: \"first\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }, {\n        id: uuidv4(),\n        title: \"Second Trimester Nutrition\",\n        description: \"Balanced diet for growing baby\",\n        content: \"Increase protein and healthy fats intake\",\n        trimester: \"second\",\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }];\n\n      // Add sample data to tables\n      this.tables.users = sampleUsers;\n      this.tables.schemes = sampleSchemes;\n      this.tables.nutrition_plans = sampleNutritionPlans;\n\n      // Save to storage\n      this.saveToStorage();\n      console.log(\"Sample data initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing sample data:\", error);\n    }\n  }\n}\n\n// Create singleton instance\nconst sqliteService = new SQLiteService();\nexport default sqliteService;", "map": {"version": 3, "names": ["v4", "uuidv4", "SQLiteService", "constructor", "db", "tables", "init", "loadFromStorage", "createTables", "initializeSampleData", "console", "log", "error", "storedData", "localStorage", "getItem", "JSON", "parse", "warn", "saveToStorage", "setItem", "stringify", "tableNames", "for<PERSON>ach", "tableName", "getAll", "table", "filters", "records", "Object", "keys", "length", "filter", "record", "every", "key", "getById", "id", "find", "find<PERSON><PERSON>", "field", "value", "create", "data", "created_at", "Date", "toISOString", "updated_at", "newRecord", "push", "update", "Error", "recordIndex", "findIndex", "delete", "splice", "clearAll", "exportData", "importData", "jsonData", "users", "sampleUsers", "username", "email", "password_hash", "role", "sampleSchemes", "title", "description", "eligibility", "benefits", "how_to_apply", "documents_required", "sampleNutritionPlans", "content", "trimester", "schemes", "nutrition_plans", "sqliteService"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/src/services/sqliteService.js"], "sourcesContent": ["import { v4 as uuidv4 } from \"uuid\";\n\n// Browser-based SQLite implementation using localStorage as fallback\nclass SQLiteService {\n  constructor() {\n    this.db = null;\n    this.tables = {};\n    this.init();\n  }\n\n  init() {\n    try {\n      // Initialize in-memory database using localStorage for persistence\n      this.loadFromStorage();\n\n      // Initialize tables\n      this.createTables();\n\n      // Initialize sample data if this is the first run\n      this.initializeSampleData();\n\n      console.log(\"Local database initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing local database:\", error);\n      throw error;\n    }\n  }\n\n  loadFromStorage() {\n    try {\n      const storedData = localStorage.getItem(\"sqlite_data\");\n      if (storedData) {\n        this.tables = JSON.parse(storedData);\n      }\n    } catch (error) {\n      console.warn(\"Could not load data from localStorage:\", error);\n      this.tables = {};\n    }\n  }\n\n  saveToStorage() {\n    try {\n      localStorage.setItem(\"sqlite_data\", JSON.stringify(this.tables));\n    } catch (error) {\n      console.warn(\"Could not save data to localStorage:\", error);\n    }\n  }\n\n  createTables() {\n    // Initialize table structures if they don't exist\n    const tableNames = [\n      \"users\",\n      \"schemes\",\n      \"plans\",\n      \"nutrition_plans\",\n      \"sleep_patterns\",\n      \"baby_care_tips\",\n      \"baby_activities\",\n      \"baby_nutrition\",\n      \"vaccinations\",\n      \"baby_milestones\",\n      \"chat_sessions\",\n      \"chat_messages\",\n      \"exercise_guides\",\n      \"weight_entries\",\n      \"pregnancy_info\",\n      \"doctor_appointments\",\n      \"faqs\",\n    ];\n\n    tableNames.forEach((tableName) => {\n      if (!this.tables[tableName]) {\n        this.tables[tableName] = [];\n      }\n    });\n\n    this.saveToStorage();\n  }\n\n  // Generic CRUD operations\n  getAll(table, filters = {}) {\n    try {\n      if (!this.tables[table]) {\n        return [];\n      }\n\n      let records = [...this.tables[table]];\n\n      // Apply filters\n      if (Object.keys(filters).length > 0) {\n        records = records.filter((record) => {\n          return Object.keys(filters).every((key) => {\n            return record[key] === filters[key];\n          });\n        });\n      }\n\n      return records;\n    } catch (error) {\n      console.error(`Error fetching all records from ${table}:`, error);\n      throw error;\n    }\n  }\n\n  getById(table, id) {\n    try {\n      if (!this.tables[table]) {\n        return null;\n      }\n\n      return this.tables[table].find((record) => record.id === id) || null;\n    } catch (error) {\n      console.error(\n        `Error fetching record with ID ${id} from ${table}:`,\n        error\n      );\n      throw error;\n    }\n  }\n\n  findBy(table, field, value) {\n    try {\n      if (!this.tables[table]) {\n        return [];\n      }\n\n      return this.tables[table].filter((record) => record[field] === value);\n    } catch (error) {\n      console.error(\n        `Error finding records in ${table} where ${field} = ${value}:`,\n        error\n      );\n      throw error;\n    }\n  }\n\n  create(table, data) {\n    try {\n      if (!this.tables[table]) {\n        this.tables[table] = [];\n      }\n\n      // Add ID and timestamps if not provided\n      if (!data.id) {\n        data.id = uuidv4();\n      }\n      if (!data.created_at) {\n        data.created_at = new Date().toISOString();\n      }\n      if (!data.updated_at) {\n        data.updated_at = new Date().toISOString();\n      }\n\n      // Create a copy of the data\n      const newRecord = { ...data };\n\n      // Add to table\n      this.tables[table].push(newRecord);\n\n      // Save to storage\n      this.saveToStorage();\n\n      // Return the created record\n      return newRecord;\n    } catch (error) {\n      console.error(`Error creating record in ${table}:`, error);\n      throw error;\n    }\n  }\n\n  update(table, id, data) {\n    try {\n      if (!this.tables[table]) {\n        throw new Error(`Table ${table} does not exist`);\n      }\n\n      const recordIndex = this.tables[table].findIndex(\n        (record) => record.id === id\n      );\n\n      if (recordIndex === -1) {\n        throw new Error(`No record found with ID ${id} in ${table}`);\n      }\n\n      // Add updated timestamp\n      data.updated_at = new Date().toISOString();\n\n      // Update the record\n      this.tables[table][recordIndex] = {\n        ...this.tables[table][recordIndex],\n        ...data,\n      };\n\n      // Save to storage\n      this.saveToStorage();\n\n      // Return the updated record\n      return this.tables[table][recordIndex];\n    } catch (error) {\n      console.error(`Error updating record with ID ${id} in ${table}:`, error);\n      throw error;\n    }\n  }\n\n  delete(table, id) {\n    try {\n      if (!this.tables[table]) {\n        throw new Error(`Table ${table} does not exist`);\n      }\n\n      const recordIndex = this.tables[table].findIndex(\n        (record) => record.id === id\n      );\n\n      if (recordIndex === -1) {\n        throw new Error(`No record found with ID ${id} in ${table}`);\n      }\n\n      // Remove the record\n      this.tables[table].splice(recordIndex, 1);\n\n      // Save to storage\n      this.saveToStorage();\n\n      return true;\n    } catch (error) {\n      console.error(\n        `Error deleting record with ID ${id} from ${table}:`,\n        error\n      );\n      throw error;\n    }\n  }\n\n  // Clear all data (useful for testing)\n  clearAll() {\n    this.tables = {};\n    this.createTables();\n  }\n\n  // Export data (useful for backup)\n  exportData() {\n    return JSON.stringify(this.tables, null, 2);\n  }\n\n  // Import data (useful for restore)\n  importData(jsonData) {\n    try {\n      this.tables = JSON.parse(jsonData);\n      this.saveToStorage();\n    } catch (error) {\n      console.error(\"Error importing data:\", error);\n      throw error;\n    }\n  }\n\n  // Initialize sample data for demonstration\n  initializeSampleData() {\n    try {\n      // Check if sample data already exists\n      if (this.tables.users && this.tables.users.length > 0) {\n        console.log(\"Sample data already exists, skipping initialization\");\n        return;\n      }\n\n      console.log(\"Initializing sample data...\");\n\n      // Sample users\n      const sampleUsers = [\n        {\n          id: uuidv4(),\n          username: \"admin\",\n          email: \"<EMAIL>\",\n          password_hash: \"admin123\",\n          role: \"admin\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n        {\n          id: uuidv4(),\n          username: \"user\",\n          email: \"<EMAIL>\",\n          password_hash: \"user123\",\n          role: \"user\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n      ];\n\n      // Sample schemes\n      const sampleSchemes = [\n        {\n          id: uuidv4(),\n          title: \"Pradhan Mantri Matru Vandana Yojana\",\n          description: \"Cash incentive for pregnant and lactating mothers\",\n          eligibility: \"Pregnant and lactating mothers for first live birth\",\n          benefits: \"₹5,000 in three installments\",\n          how_to_apply: \"Apply through Anganwadi centers or online portal\",\n          documents_required: \"Aadhaar, Bank account, Pregnancy certificate\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n        {\n          id: uuidv4(),\n          title: \"Janani Suraksha Yojana\",\n          description:\n            \"Safe motherhood intervention under National Health Mission\",\n          eligibility: \"Pregnant women belonging to BPL families\",\n          benefits: \"Cash assistance for institutional delivery\",\n          how_to_apply: \"Through accredited health institutions\",\n          documents_required: \"BPL card, Aadhaar, Bank account details\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n      ];\n\n      // Sample nutrition plans\n      const sampleNutritionPlans = [\n        {\n          id: uuidv4(),\n          title: \"First Trimester Nutrition\",\n          description: \"Essential nutrients for early pregnancy\",\n          content: \"Focus on folic acid, iron, and calcium rich foods\",\n          trimester: \"first\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n        {\n          id: uuidv4(),\n          title: \"Second Trimester Nutrition\",\n          description: \"Balanced diet for growing baby\",\n          content: \"Increase protein and healthy fats intake\",\n          trimester: \"second\",\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        },\n      ];\n\n      // Add sample data to tables\n      this.tables.users = sampleUsers;\n      this.tables.schemes = sampleSchemes;\n      this.tables.nutrition_plans = sampleNutritionPlans;\n\n      // Save to storage\n      this.saveToStorage();\n\n      console.log(\"Sample data initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing sample data:\", error);\n    }\n  }\n}\n\n// Create singleton instance\nconst sqliteService = new SQLiteService();\n\nexport default sqliteService;\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,MAAM,QAAQ,MAAM;;AAEnC;AACA,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,IAAI,CAAC,CAAC;EACb;EAEAA,IAAIA,CAAA,EAAG;IACL,IAAI;MACF;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;;MAEtB;MACA,IAAI,CAACC,YAAY,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAE3BC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;EAEAL,eAAeA,CAAA,EAAG;IAChB,IAAI;MACF,MAAMM,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MACtD,IAAIF,UAAU,EAAE;QACd,IAAI,CAACR,MAAM,GAAGW,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MACtC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdF,OAAO,CAACQ,IAAI,CAAC,wCAAwC,EAAEN,KAAK,CAAC;MAC7D,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;IAClB;EACF;EAEAc,aAAaA,CAAA,EAAG;IACd,IAAI;MACFL,YAAY,CAACM,OAAO,CAAC,aAAa,EAAEJ,IAAI,CAACK,SAAS,CAAC,IAAI,CAAChB,MAAM,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdF,OAAO,CAACQ,IAAI,CAAC,sCAAsC,EAAEN,KAAK,CAAC;IAC7D;EACF;EAEAJ,YAAYA,CAAA,EAAG;IACb;IACA,MAAMc,UAAU,GAAG,CACjB,OAAO,EACP,SAAS,EACT,OAAO,EACP,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,MAAM,CACP;IAEDA,UAAU,CAACC,OAAO,CAAEC,SAAS,IAAK;MAChC,IAAI,CAAC,IAAI,CAACnB,MAAM,CAACmB,SAAS,CAAC,EAAE;QAC3B,IAAI,CAACnB,MAAM,CAACmB,SAAS,CAAC,GAAG,EAAE;MAC7B;IACF,CAAC,CAAC;IAEF,IAAI,CAACL,aAAa,CAAC,CAAC;EACtB;;EAEA;EACAM,MAAMA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI;MACF,IAAI,CAAC,IAAI,CAACtB,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MAEA,IAAIE,OAAO,GAAG,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACqB,KAAK,CAAC,CAAC;;MAErC;MACA,IAAIG,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;QACnCH,OAAO,GAAGA,OAAO,CAACI,MAAM,CAAEC,MAAM,IAAK;UACnC,OAAOJ,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACO,KAAK,CAAEC,GAAG,IAAK;YACzC,OAAOF,MAAM,CAACE,GAAG,CAAC,KAAKR,OAAO,CAACQ,GAAG,CAAC;UACrC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA,OAAOP,OAAO;IAChB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmCc,KAAK,GAAG,EAAEd,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;EAEAwB,OAAOA,CAACV,KAAK,EAAEW,EAAE,EAAE;IACjB,IAAI;MACF,IAAI,CAAC,IAAI,CAAChC,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,OAAO,IAAI;MACb;MAEA,OAAO,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC,CAACY,IAAI,CAAEL,MAAM,IAAKA,MAAM,CAACI,EAAE,KAAKA,EAAE,CAAC,IAAI,IAAI;IACtE,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CACX,iCAAiCyB,EAAE,SAASX,KAAK,GAAG,EACpDd,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF;EAEA2B,MAAMA,CAACb,KAAK,EAAEc,KAAK,EAAEC,KAAK,EAAE;IAC1B,IAAI;MACF,IAAI,CAAC,IAAI,CAACpC,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MAEA,OAAO,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC,CAACM,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACO,KAAK,CAAC,KAAKC,KAAK,CAAC;IACvE,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CACX,4BAA4Bc,KAAK,UAAUc,KAAK,MAAMC,KAAK,GAAG,EAC9D7B,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF;EAEA8B,MAAMA,CAAChB,KAAK,EAAEiB,IAAI,EAAE;IAClB,IAAI;MACF,IAAI,CAAC,IAAI,CAACtC,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC,GAAG,EAAE;MACzB;;MAEA;MACA,IAAI,CAACiB,IAAI,CAACN,EAAE,EAAE;QACZM,IAAI,CAACN,EAAE,GAAGpC,MAAM,CAAC,CAAC;MACpB;MACA,IAAI,CAAC0C,IAAI,CAACC,UAAU,EAAE;QACpBD,IAAI,CAACC,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5C;MACA,IAAI,CAACH,IAAI,CAACI,UAAU,EAAE;QACpBJ,IAAI,CAACI,UAAU,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5C;;MAEA;MACA,MAAME,SAAS,GAAG;QAAE,GAAGL;MAAK,CAAC;;MAE7B;MACA,IAAI,CAACtC,MAAM,CAACqB,KAAK,CAAC,CAACuB,IAAI,CAACD,SAAS,CAAC;;MAElC;MACA,IAAI,CAAC7B,aAAa,CAAC,CAAC;;MAEpB;MACA,OAAO6B,SAAS;IAClB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,4BAA4Bc,KAAK,GAAG,EAAEd,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;EAEAsC,MAAMA,CAACxB,KAAK,EAAEW,EAAE,EAAEM,IAAI,EAAE;IACtB,IAAI;MACF,IAAI,CAAC,IAAI,CAACtC,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,MAAM,IAAIyB,KAAK,CAAC,SAASzB,KAAK,iBAAiB,CAAC;MAClD;MAEA,MAAM0B,WAAW,GAAG,IAAI,CAAC/C,MAAM,CAACqB,KAAK,CAAC,CAAC2B,SAAS,CAC7CpB,MAAM,IAAKA,MAAM,CAACI,EAAE,KAAKA,EAC5B,CAAC;MAED,IAAIe,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,MAAM,IAAID,KAAK,CAAC,2BAA2Bd,EAAE,OAAOX,KAAK,EAAE,CAAC;MAC9D;;MAEA;MACAiB,IAAI,CAACI,UAAU,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;MAE1C;MACA,IAAI,CAACzC,MAAM,CAACqB,KAAK,CAAC,CAAC0B,WAAW,CAAC,GAAG;QAChC,GAAG,IAAI,CAAC/C,MAAM,CAACqB,KAAK,CAAC,CAAC0B,WAAW,CAAC;QAClC,GAAGT;MACL,CAAC;;MAED;MACA,IAAI,CAACxB,aAAa,CAAC,CAAC;;MAEpB;MACA,OAAO,IAAI,CAACd,MAAM,CAACqB,KAAK,CAAC,CAAC0B,WAAW,CAAC;IACxC,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iCAAiCyB,EAAE,OAAOX,KAAK,GAAG,EAAEd,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;EAEA0C,MAAMA,CAAC5B,KAAK,EAAEW,EAAE,EAAE;IAChB,IAAI;MACF,IAAI,CAAC,IAAI,CAAChC,MAAM,CAACqB,KAAK,CAAC,EAAE;QACvB,MAAM,IAAIyB,KAAK,CAAC,SAASzB,KAAK,iBAAiB,CAAC;MAClD;MAEA,MAAM0B,WAAW,GAAG,IAAI,CAAC/C,MAAM,CAACqB,KAAK,CAAC,CAAC2B,SAAS,CAC7CpB,MAAM,IAAKA,MAAM,CAACI,EAAE,KAAKA,EAC5B,CAAC;MAED,IAAIe,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,MAAM,IAAID,KAAK,CAAC,2BAA2Bd,EAAE,OAAOX,KAAK,EAAE,CAAC;MAC9D;;MAEA;MACA,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC,CAAC6B,MAAM,CAACH,WAAW,EAAE,CAAC,CAAC;;MAEzC;MACA,IAAI,CAACjC,aAAa,CAAC,CAAC;MAEpB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CACX,iCAAiCyB,EAAE,SAASX,KAAK,GAAG,EACpDd,KACF,CAAC;MACD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA4C,QAAQA,CAAA,EAAG;IACT,IAAI,CAACnD,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACG,YAAY,CAAC,CAAC;EACrB;;EAEA;EACAiD,UAAUA,CAAA,EAAG;IACX,OAAOzC,IAAI,CAACK,SAAS,CAAC,IAAI,CAAChB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;EAC7C;;EAEA;EACAqD,UAAUA,CAACC,QAAQ,EAAE;IACnB,IAAI;MACF,IAAI,CAACtD,MAAM,GAAGW,IAAI,CAACC,KAAK,CAAC0C,QAAQ,CAAC;MAClC,IAAI,CAACxC,aAAa,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;EACAH,oBAAoBA,CAAA,EAAG;IACrB,IAAI;MACF;MACA,IAAI,IAAI,CAACJ,MAAM,CAACuD,KAAK,IAAI,IAAI,CAACvD,MAAM,CAACuD,KAAK,CAAC7B,MAAM,GAAG,CAAC,EAAE;QACrDrB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAClE;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,MAAMkD,WAAW,GAAG,CAClB;QACExB,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZ6D,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,oBAAoB;QAC3BC,aAAa,EAAE,UAAU;QACzBC,IAAI,EAAE,OAAO;QACbrB,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACET,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZ6D,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,mBAAmB;QAC1BC,aAAa,EAAE,SAAS;QACxBC,IAAI,EAAE,MAAM;QACZrB,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;;MAED;MACA,MAAMoB,aAAa,GAAG,CACpB;QACE7B,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZkE,KAAK,EAAE,qCAAqC;QAC5CC,WAAW,EAAE,mDAAmD;QAChEC,WAAW,EAAE,qDAAqD;QAClEC,QAAQ,EAAE,8BAA8B;QACxCC,YAAY,EAAE,kDAAkD;QAChEC,kBAAkB,EAAE,8CAA8C;QAClE5B,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACET,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZkE,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EACT,4DAA4D;QAC9DC,WAAW,EAAE,0CAA0C;QACvDC,QAAQ,EAAE,4CAA4C;QACtDC,YAAY,EAAE,wCAAwC;QACtDC,kBAAkB,EAAE,yCAAyC;QAC7D5B,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;;MAED;MACA,MAAM2B,oBAAoB,GAAG,CAC3B;QACEpC,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZkE,KAAK,EAAE,2BAA2B;QAClCC,WAAW,EAAE,yCAAyC;QACtDM,OAAO,EAAE,mDAAmD;QAC5DC,SAAS,EAAE,OAAO;QAClB/B,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACET,EAAE,EAAEpC,MAAM,CAAC,CAAC;QACZkE,KAAK,EAAE,4BAA4B;QACnCC,WAAW,EAAE,gCAAgC;QAC7CM,OAAO,EAAE,0CAA0C;QACnDC,SAAS,EAAE,QAAQ;QACnB/B,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;;MAED;MACA,IAAI,CAACzC,MAAM,CAACuD,KAAK,GAAGC,WAAW;MAC/B,IAAI,CAACxD,MAAM,CAACuE,OAAO,GAAGV,aAAa;MACnC,IAAI,CAAC7D,MAAM,CAACwE,eAAe,GAAGJ,oBAAoB;;MAElD;MACA,IAAI,CAACtD,aAAa,CAAC,CAAC;MAEpBT,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF;AACF;;AAEA;AACA,MAAMkE,aAAa,GAAG,IAAI5E,aAAa,CAAC,CAAC;AAEzC,eAAe4E,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}