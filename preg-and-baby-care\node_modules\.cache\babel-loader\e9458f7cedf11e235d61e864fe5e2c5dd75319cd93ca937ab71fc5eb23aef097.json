{"ast": null, "code": "'use strict';\n\nmodule.exports = require('./database');\nmodule.exports.SqliteError = require('./sqlite-error');", "map": {"version": 3, "names": ["module", "exports", "require", "SqliteError"], "sources": ["C:/Users/<USER>/Desktop/final/Upd/preg-and-baby-care/node_modules/better-sqlite3/lib/index.js"], "sourcesContent": ["'use strict';\nmodule.exports = require('./database');\nmodule.exports.SqliteError = require('./sqlite-error');\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;AACtCF,MAAM,CAACC,OAAO,CAACE,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}