// DEPRECATED: This file is no longer used
// The application now uses SQLite instead of Supabase
// This file is kept for reference only

/*
import { createClient } from "@supabase/supabase-js";

// Supabase URL and anon key should be stored in environment variables
// For development, we can use these directly
const supabaseUrl =
  process.env.REACT_APP_SUPABASE_URL ||
  "https://tlyrbztcbezhnwoqmvov.supabase.co";
const supabaseAnonKey =
  process.env.REACT_APP_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRseXJienRjYmV6aG53b3Ftdm92Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzMzc5MDMsImV4cCI6MjA2MDkxMzkwM30.mAlNniFZmA4Zg8axNroA_Ltu_STBO5COR36dsu5FQw4";

// Create a single supabase client for interacting with your database
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default supabase;
*/

// Placeholder export to prevent import errors
export default null;
