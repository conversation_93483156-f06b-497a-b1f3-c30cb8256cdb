{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 20a6 6 0 0 0-12 0\",\n  key: \"1qehca\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"4\",\n  key: \"1h16sb\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]];\nconst CircleUserRound = createLucideIcon(\"circle-user-round\", __iconNode);\nexport { __iconNode, CircleUserRound as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "CircleUserRound", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\final\\Upd\\preg-and-baby-care\\node_modules\\lucide-react\\src\\icons\\circle-user-round.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 20a6 6 0 0 0-12 0', key: '1qehca' }],\n  ['circle', { cx: '12', cy: '10', r: '4', key: '1h16sb' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name CircleUserRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMjBhNiA2IDAgMCAwLTEyIDAiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMCIgcj0iNCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle-user-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleUserRound = createLucideIcon('circle-user-round', __iconNode);\n\nexport default CircleUserRound;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMH,GAAK;AAAA,CAAU,EAC3D;AAaM,MAAAI,eAAA,GAAkBC,gBAAiB,sBAAqBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}